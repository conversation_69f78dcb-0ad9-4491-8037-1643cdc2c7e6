# AI五子棋训练和对战系统

一个基于深度学习的五子棋AI训练和对战平台，支持自我对弈训练、实时对战和训练监控。

## 🌟 功能特性

### 🎮 游戏功能
- **智能对战**: 用户vs AI对战，支持多种难度级别
- **标准棋盘**: 15x15五子棋棋盘，符合国际标准
- **实时同步**: WebSocket实现的实时游戏状态同步
- **规则验证**: 完整的游戏规则验证和胜负判定
- **移动历史**: 完整的移动记录和回放功能
- **智能提示**: AI提供的移动建议和策略分析

### 🤖 AI训练系统
- **自我对弈**: 两个AI模型相互对战生成训练数据
- **强化学习**: 基于AlphaZero的强化学习算法
- **模型架构**: 支持CNN、ResNet、Transformer多种架构
- **版本管理**: 完整的模型版本控制和性能追踪
- **数据收集**: 自动收集和存储训练数据

### 📊 管理员控制面板
- **训练配置**: 灵活的训练参数设置
- **实时监控**: 训练进度和性能指标实时显示
- **性能评估**: 模型对战评估和胜率统计
- **数据可视化**: 训练历史和性能趋势图表
- **系统管理**: 数据备份、清理和系统设置

## 🛠 技术栈

- **前端**: D3.js + HTML5 + CSS3 + JavaScript
- **后端**: Python Flask + Flask-SocketIO
- **AI框架**: PyTorch + 强化学习
- **数据库**: SQLite (可扩展为PostgreSQL)
- **通信**: WebSocket实时双向通信
- **部署**: Docker + Nginx (可选)

## 📁 项目结构

```
ai-gomoku-system/
├── backend/                 # 后端Flask应用
│   ├── app/
│   │   ├── __init__.py     # Flask应用工厂
│   │   ├── models/         # 数据模型
│   │   │   ├── game.py     # 游戏相关模型
│   │   │   ├── training.py # 训练相关模型
│   │   │   └── model.py    # AI模型管理
│   │   ├── api/           # RESTful API
│   │   │   ├── game_api.py # 游戏API
│   │   │   ├── training_api.py # 训练API
│   │   │   └── model_api.py # 模型API
│   │   ├── websocket/     # WebSocket处理
│   │   │   ├── game_events.py # 游戏事件
│   │   │   └── training_events.py # 训练事件
│   │   └── utils/         # 工具函数
│   │       └── database.py # 数据库工具
│   ├── ai/                # AI模型和训练
│   │   ├── model/         # 神经网络模型
│   │   │   ├── network.py  # 网络架构
│   │   │   └── manager.py  # 模型管理器
│   │   ├── training/      # 训练算法
│   │   │   ├── trainer.py  # 训练器
│   │   │   ├── self_play.py # 自我对弈
│   │   │   └── experience.py # 经验回放
│   │   └── agents/        # AI代理
│   │       ├── mcts_agent.py # MCTS代理
│   │       ├── neural_agent.py # 神经网络代理
│   │       └── random_agent.py # 随机代理
│   ├── game/              # 游戏逻辑
│   │   ├── board.py       # 棋盘逻辑
│   │   ├── rules.py       # 游戏规则
│   │   └── engine.py      # 游戏引擎
│   ├── run.py             # 应用启动文件
│   └── requirements.txt   # Python依赖
├── frontend/              # 前端应用
│   ├── static/
│   │   ├── css/
│   │   │   ├── style.css   # 主样式
│   │   │   └── admin.css   # 管理面板样式
│   │   ├── js/
│   │   │   ├── config.js   # 配置文件
│   │   │   ├── websocket.js # WebSocket管理
│   │   │   ├── board.js    # D3.js棋盘
│   │   │   ├── game.js     # 游戏逻辑
│   │   │   ├── ui.js       # UI工具
│   │   │   ├── admin.js    # 管理面板
│   │   │   └── main.js     # 主入口
│   │   └── assets/        # 静态资源
│   └── templates/
│       ├── index.html     # 游戏主页
│       └── admin.html     # 管理面板
├── data/                  # 数据存储
│   ├── models/           # 训练好的模型
│   ├── games/            # 游戏记录
│   └── training/         # 训练数据
├── config/               # 配置文件
│   └── config.py         # 应用配置
├── tests/               # 测试文件
│   └── test_basic.py     # 基础测试
├── logs/                # 日志文件
├── start.py             # 启动脚本
├── .env.example         # 环境变量示例
└── README.md           # 项目说明
```

## 🚀 快速开始

### 环境要求
- **Python**: 3.8+ (推荐3.9+)
- **内存**: 最少4GB RAM (训练时推荐8GB+)
- **存储**: 最少2GB可用空间
- **GPU**: 可选，用于加速训练 (支持CUDA)

### 方法一：使用启动脚本 (推荐)

```bash
# 克隆项目
git clone <repository-url>
cd ai-gomoku-system

# 使用启动脚本自动设置和启动
python start.py --install --init-db

# 或者分步执行
python start.py --setup-only  # 仅设置环境
python start.py --install     # 安装依赖
python start.py --init-db     # 初始化数据库
python start.py              # 启动服务器
```

### 方法二：手动安装

```bash
# 1. 安装Python依赖
pip install -r backend/requirements.txt

# 2. 设置环境变量
cp .env.example .env
# 编辑.env文件设置你的配置

# 3. 初始化数据库
cd backend
python -c "from app.utils.database import init_database; init_database()"

# 4. 启动服务器
python run.py
```

### 访问应用

启动成功后，在浏览器中访问：

- **游戏界面**: http://localhost:5000
- **管理面板**: http://localhost:5000/admin
- **API文档**: http://localhost:5000/api/docs (如果启用)

## 🎯 使用指南

### 开始游戏

1. 打开游戏界面 (http://localhost:5000)
2. 选择对手类型 (AI/人类) 和难度
3. 点击"开始游戏"
4. 在棋盘上点击放置棋子
5. 使用"提示"按钮获取AI建议

### 训练AI模型

1. 打开管理面板 (http://localhost:5000/admin)
2. 进入"训练管理"页面
3. 点击"开始新训练"
4. 配置训练参数：
   - 模型名称和架构
   - 训练轮数和批次大小
   - 学习率和自我对弈局数
5. 点击"开始训练"
6. 在概览页面监控训练进度

### 模型管理

1. 在管理面板进入"模型管理"
2. 查看所有训练好的模型
3. 激活想要使用的模型
4. 评估模型性能
5. 下载或删除模型

## ⚙️ 配置说明

### 环境变量配置

编辑 `.env` 文件设置以下参数：

```bash
# Flask配置
FLASK_ENV=development          # 开发模式
SECRET_KEY=your-secret-key     # 密钥

# 数据库配置
DATABASE_URL=sqlite:///data/app.db  # 数据库URL

# AI训练配置
DEVICE=cuda                    # 使用GPU (cuda) 或CPU (cpu)
BATCH_SIZE=32                  # 批次大小
LEARNING_RATE=0.001           # 学习率
EPOCHS=1000                   # 训练轮数

# 日志配置
LOG_LEVEL=INFO                # 日志级别
```

### 训练参数调优

- **批次大小**: 32-64 (根据内存调整)
- **学习率**: 0.001-0.01 (从小开始)
- **自我对弈局数**: 100-500 (更多数据更好)
- **模型架构**: ResNet推荐用于最佳性能

## 🧪 测试

运行测试确保系统正常工作：

```bash
# 运行所有测试
python start.py --test

# 或手动运行
pytest tests/ -v

# 运行特定测试
pytest tests/test_basic.py -v
```

## 📈 性能优化

### 训练优化
- 使用GPU加速训练 (设置 `DEVICE=cuda`)
- 增加批次大小 (如果内存允许)
- 使用更多的自我对弈数据
- 调整学习率和网络架构

### 系统优化
- 定期清理旧的训练数据
- 使用SSD存储提高I/O性能
- 增加系统内存
- 使用专业GPU (如RTX系列)

## 🔧 故障排除

### 常见问题

**Q: 启动时提示依赖包缺失**
```bash
# 重新安装依赖
pip install -r backend/requirements.txt
```

**Q: 数据库错误**
```bash
# 重新初始化数据库
python start.py --init-db
```

**Q: WebSocket连接失败**
- 检查防火墙设置
- 确认端口5000未被占用
- 检查浏览器控制台错误信息

**Q: AI训练速度慢**
- 检查是否使用GPU (`DEVICE=cuda`)
- 减少批次大小或模型复杂度
- 确保有足够的系统资源

**Q: 内存不足**
- 减少批次大小
- 减少自我对弈并发数
- 清理旧的训练数据

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- AlphaZero论文提供的算法灵感
- D3.js社区提供的可视化支持
- PyTorch团队提供的深度学习框架
- Flask社区提供的Web框架支持

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 加入讨论群

---

**享受与AI的五子棋对战吧！** 🎮✨
