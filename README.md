# AI五子棋训练和对战系统

一个基于深度学习的五子棋AI训练和对战平台，支持自我对弈训练、实时对战和训练监控。

## 技术栈

- **前端**: D3.js + HTML5 + CSS3 + JavaScript
- **后端**: Python Flask + Flask-SocketIO
- **AI模型**: PyTorch + 强化学习
- **数据库**: SQLite (可扩展为PostgreSQL)
- **通信**: WebSocket实时双向通信

## 功能特性

### 🎮 游戏功能
- 单人模式：用户vs AI对战
- 标准15x15五子棋棋盘
- 实时游戏状态同步
- 完整的游戏规则验证和胜负判定

### 🤖 AI训练系统
- 自我对弈训练机制
- 强化学习算法优化
- 模型版本管理
- 训练数据自动收集和存储

### 📊 管理员控制面板
- 训练参数配置
- 实时训练进度监控
- 模型性能评估
- 训练历史可视化

## 项目结构

```
ai-gomoku-system/
├── backend/                 # 后端Flask应用
│   ├── app/
│   │   ├── __init__.py
│   │   ├── models/         # 数据模型
│   │   ├── api/           # RESTful API
│   │   ├── websocket/     # WebSocket处理
│   │   └── utils/         # 工具函数
│   ├── ai/                # AI模型和训练
│   │   ├── model/         # 神经网络模型
│   │   ├── training/      # 训练算法
│   │   └── agents/        # AI代理
│   ├── game/              # 游戏逻辑
│   │   ├── board.py       # 棋盘逻辑
│   │   ├── rules.py       # 游戏规则
│   │   └── engine.py      # 游戏引擎
│   └── requirements.txt
├── frontend/              # 前端应用
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── assets/
│   ├── templates/
│   └── index.html
├── data/                  # 数据存储
│   ├── models/           # 训练好的模型
│   ├── games/            # 游戏记录
│   └── training/         # 训练数据
├── config/               # 配置文件
├── tests/               # 测试文件
└── docs/                # 文档
```

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 14+ (用于前端开发工具)
- 推荐使用GPU进行AI训练

### 安装依赖
```bash
# 后端依赖
cd backend
pip install -r requirements.txt

# 启动开发服务器
python run.py
```

### 访问应用
- 游戏界面: http://localhost:5000
- 管理面板: http://localhost:5000/admin
- API文档: http://localhost:5000/api/docs

## 开发指南

详细的开发文档请参考 `docs/` 目录。

## 许可证

MIT License
