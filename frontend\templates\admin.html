<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI五子棋训练管理面板</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>AI训练管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#dashboard" class="nav-link active" data-section="dashboard">
                        <span class="icon">📊</span>概览
                    </a></li>
                    <li><a href="#training" class="nav-link" data-section="training">
                        <span class="icon">🎯</span>训练管理
                    </a></li>
                    <li><a href="#models" class="nav-link" data-section="models">
                        <span class="icon">🧠</span>模型管理
                    </a></li>
                    <li><a href="#evaluation" class="nav-link" data-section="evaluation">
                        <span class="icon">⚖️</span>性能评估
                    </a></li>
                    <li><a href="#games" class="nav-link" data-section="games">
                        <span class="icon">🎮</span>游戏记录
                    </a></li>
                    <li><a href="#settings" class="nav-link" data-section="settings">
                        <span class="icon">⚙️</span>系统设置
                    </a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <a href="/" class="btn btn-secondary">返回游戏</a>
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="admin-header">
                <h1 id="pageTitle">系统概览</h1>
                <div class="header-actions">
                    <div class="connection-status" id="connectionStatus">
                        <span class="status-indicator" id="statusIndicator"></span>
                        <span id="statusText">连接中...</span>
                    </div>
                </div>
            </header>

            <!-- 概览页面 -->
            <section id="dashboard-section" class="content-section active">
                <div class="dashboard-grid">
                    <!-- 统计卡片 -->
                    <div class="stats-cards">
                        <div class="stat-card">
                            <div class="stat-icon">🎯</div>
                            <div class="stat-content">
                                <h3>训练会话</h3>
                                <div class="stat-number" id="totalSessions">0</div>
                                <div class="stat-label">总数</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🧠</div>
                            <div class="stat-content">
                                <h3>AI模型</h3>
                                <div class="stat-number" id="totalModels">0</div>
                                <div class="stat-label">已训练</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🎮</div>
                            <div class="stat-content">
                                <h3>对战游戏</h3>
                                <div class="stat-number" id="totalGames">0</div>
                                <div class="stat-label">总局数</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📈</div>
                            <div class="stat-content">
                                <h3>AI胜率</h3>
                                <div class="stat-number" id="aiWinRate">0%</div>
                                <div class="stat-label">当前模型</div>
                            </div>
                        </div>
                    </div>

                    <!-- 训练进度 -->
                    <div class="training-progress-card">
                        <h3>当前训练进度</h3>
                        <div id="currentTraining">
                            <div class="no-training">暂无进行中的训练</div>
                        </div>
                    </div>

                    <!-- 性能图表 -->
                    <div class="chart-card">
                        <h3>模型性能趋势</h3>
                        <canvas id="performanceChart"></canvas>
                    </div>
                </div>
            </section>

            <!-- 训练管理页面 -->
            <section id="training-section" class="content-section">
                <div class="training-controls">
                    <button id="startTrainingBtn" class="btn btn-success">开始新训练</button>
                    <button id="stopTrainingBtn" class="btn btn-danger" disabled>停止训练</button>
                </div>

                <!-- 训练配置 -->
                <div class="training-config" id="trainingConfig" style="display: none;">
                    <h3>训练配置</h3>
                    <form id="trainingForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="modelName">模型名称</label>
                                <input type="text" id="modelName" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="modelArchitecture">模型架构</label>
                                <select id="modelArchitecture" class="form-control">
                                    <option value="resnet">ResNet</option>
                                    <option value="cnn">CNN</option>
                                    <option value="transformer">Transformer</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="epochs">训练轮数</label>
                                <input type="number" id="epochs" class="form-control" value="1000" min="1">
                            </div>
                            <div class="form-group">
                                <label for="batchSize">批次大小</label>
                                <input type="number" id="batchSize" class="form-control" value="32" min="1">
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="learningRate">学习率</label>
                                <input type="number" id="learningRate" class="form-control" value="0.001" step="0.0001">
                            </div>
                            <div class="form-group">
                                <label for="selfPlayGames">自我对弈局数</label>
                                <input type="number" id="selfPlayGames" class="form-control" value="100" min="1">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">开始训练</button>
                            <button type="button" id="cancelTraining" class="btn btn-secondary">取消</button>
                        </div>
                    </form>
                </div>

                <!-- 训练历史 -->
                <div class="training-history">
                    <h3>训练历史</h3>
                    <div class="table-container">
                        <table id="trainingTable" class="data-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>模型名称</th>
                                    <th>状态</th>
                                    <th>开始时间</th>
                                    <th>完成时间</th>
                                    <th>轮数</th>
                                    <th>最终损失</th>
                                    <th>胜率</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- 模型管理页面 -->
            <section id="models-section" class="content-section">
                <div class="models-controls">
                    <button id="refreshModels" class="btn btn-info">刷新列表</button>
                    <button id="uploadModel" class="btn btn-primary">上传模型</button>
                </div>

                <div class="models-grid">
                    <div class="models-list">
                        <h3>模型列表</h3>
                        <div class="table-container">
                            <table id="modelsTable" class="data-table">
                                <thead>
                                    <tr>
                                        <th>名称</th>
                                        <th>版本</th>
                                        <th>架构</th>
                                        <th>状态</th>
                                        <th>胜率</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>

                    <div class="model-details">
                        <h3>模型详情</h3>
                        <div id="modelDetailsContent">
                            <p>请选择一个模型查看详情</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 性能评估页面 -->
            <section id="evaluation-section" class="content-section">
                <div class="evaluation-controls">
                    <button id="startEvaluation" class="btn btn-success">开始评估</button>
                </div>

                <div class="evaluation-config" id="evaluationConfig" style="display: none;">
                    <h3>评估配置</h3>
                    <form id="evaluationForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="evalModel">评估模型</label>
                                <select id="evalModel" class="form-control" required></select>
                            </div>
                            <div class="form-group">
                                <label for="opponentModel">对手模型</label>
                                <select id="opponentModel" class="form-control">
                                    <option value="">随机对手</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="evalGames">评估局数</label>
                                <input type="number" id="evalGames" class="form-control" value="100" min="1">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">开始评估</button>
                            <button type="button" id="cancelEvaluation" class="btn btn-secondary">取消</button>
                        </div>
                    </form>
                </div>

                <div class="evaluation-results">
                    <h3>评估结果</h3>
                    <div id="evaluationResults">
                        <p>暂无评估结果</p>
                    </div>
                </div>
            </section>

            <!-- 游戏记录页面 -->
            <section id="games-section" class="content-section">
                <div class="games-controls">
                    <button id="refreshGames" class="btn btn-info">刷新记录</button>
                    <button id="exportGames" class="btn btn-secondary">导出数据</button>
                </div>

                <div class="table-container">
                    <table id="gamesTable" class="data-table">
                        <thead>
                            <tr>
                                <th>游戏ID</th>
                                <th>对手类型</th>
                                <th>难度</th>
                                <th>状态</th>
                                <th>获胜者</th>
                                <th>步数</th>
                                <th>开始时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </section>

            <!-- 系统设置页面 -->
            <section id="settings-section" class="content-section">
                <div class="settings-grid">
                    <div class="settings-group">
                        <h3>系统配置</h3>
                        <div class="setting-item">
                            <label>自动保存间隔 (秒)</label>
                            <input type="number" id="autoSaveInterval" class="form-control" value="300">
                        </div>
                        <div class="setting-item">
                            <label>最大并发训练数</label>
                            <input type="number" id="maxConcurrentTraining" class="form-control" value="1">
                        </div>
                        <div class="setting-item">
                            <label>日志级别</label>
                            <select id="logLevel" class="form-control">
                                <option value="debug">Debug</option>
                                <option value="info" selected>Info</option>
                                <option value="warn">Warning</option>
                                <option value="error">Error</option>
                            </select>
                        </div>
                    </div>

                    <div class="settings-group">
                        <h3>数据管理</h3>
                        <div class="setting-item">
                            <button id="backupData" class="btn btn-info">备份数据</button>
                            <p class="setting-description">备份所有训练数据和模型</p>
                        </div>
                        <div class="setting-item">
                            <button id="cleanupData" class="btn btn-warning">清理旧数据</button>
                            <p class="setting-description">清理30天前的训练记录</p>
                        </div>
                        <div class="setting-item">
                            <button id="resetSystem" class="btn btn-danger">重置系统</button>
                            <p class="setting-description">⚠️ 这将删除所有数据</p>
                        </div>
                    </div>
                </div>

                <div class="settings-actions">
                    <button id="saveSettings" class="btn btn-success">保存设置</button>
                </div>
            </section>
        </main>
    </div>

    <!-- 脚本文件 -->
    <script src="{{ url_for('static', filename='js/config.js') }}"></script>
    <script src="{{ url_for('static', filename='js/websocket.js') }}"></script>
    <script src="{{ url_for('static', filename='js/admin.js') }}"></script>
</body>
</html>
