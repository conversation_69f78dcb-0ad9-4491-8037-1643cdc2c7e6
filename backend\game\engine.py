"""
五子棋游戏引擎
"""
from typing import Dict, List, Tuple, Optional, Any
from game.board import Board, Player
from game.rules import GameRules
from app.models.game import Game, GameMove
from app import db
from datetime import datetime
import time

class GameEngine:
    """五子棋游戏引擎"""
    
    def __init__(self, game_id: int):
        """
        初始化游戏引擎
        
        Args:
            game_id: 游戏ID
        """
        self.game_id = game_id
        self.board = Board()
        self.rules = GameRules()
        self._load_game_state()
    
    def _load_game_state(self):
        """从数据库加载游戏状态"""
        game = Game.query.get(self.game_id)
        if not game:
            raise ValueError(f"Game {self.game_id} not found")
        
        # 重置棋盘
        self.board.reset()
        
        # 重放所有移动
        moves = GameMove.query.filter_by(game_id=self.game_id).order_by(GameMove.move_number).all()
        for move in moves:
            player = Player.BLACK if move.player == 'black' else Player.WHITE
            self.board.make_move(move.x, move.y, player)
    
    def is_valid_move(self, x: int, y: int) -> bool:
        """
        检查移动是否有效
        
        Args:
            x, y: 移动位置
            
        Returns:
            bool: 移动是否有效
        """
        return self.rules.is_valid_move(self.board, x, y)
    
    def make_move(self, x: int, y: int, player_str: str) -> Dict[str, Any]:
        """
        执行移动
        
        Args:
            x, y: 移动位置
            player_str: 玩家字符串 ('black' 或 'white')
            
        Returns:
            Dict[str, Any]: 移动结果
        """
        try:
            player = Player.BLACK if player_str == 'black' else Player.WHITE
            
            # 验证移动
            if not self.is_valid_move(x, y):
                return {'success': False, 'error': 'Invalid move'}
            
            # 执行移动
            if not self.board.make_move(x, y, player):
                return {'success': False, 'error': 'Failed to make move'}
            
            # 保存移动到数据库
            move_number = len(self.board.move_history)
            game_move = GameMove(
                game_id=self.game_id,
                move_number=move_number,
                player=player_str,
                x=x,
                y=y,
                timestamp=datetime.utcnow()
            )
            db.session.add(game_move)
            
            # 检查游戏状态
            winner = self.board.check_winner()
            game_status = 'active'
            next_player = 'white' if player_str == 'black' else 'black'
            
            result = {
                'success': True,
                'game_status': game_status,
                'next_player': next_player
            }
            
            # 更新游戏状态
            game = Game.query.get(self.game_id)
            
            if winner:
                game_status = 'finished'
                winner_str = 'black' if winner == Player.BLACK else 'white'
                game.status = 'finished'
                game.winner = winner_str
                result['game_status'] = 'finished'
                result['winner'] = winner_str
                result['reason'] = 'normal'
            elif self.rules.is_draw(self.board):
                game_status = 'finished'
                game.status = 'finished'
                game.winner = 'draw'
                result['game_status'] = 'finished'
                result['winner'] = 'draw'
                result['reason'] = 'draw'
            else:
                game.current_player = next_player
            
            game.updated_at = datetime.utcnow()
            db.session.commit()
            
            # 如果是对AI游戏且轮到AI，生成AI移动
            if (game_status == 'active' and 
                game.player_type == 'ai' and 
                next_player == 'white'):  # 假设AI是白棋
                ai_move = self._generate_ai_move()
                if ai_move:
                    result['ai_move'] = ai_move
            
            return result
            
        except Exception as e:
            db.session.rollback()
            return {'success': False, 'error': str(e)}
    
    def _generate_ai_move(self) -> Optional[Dict[str, Any]]:
        """
        生成AI移动
        
        Returns:
            Optional[Dict[str, Any]]: AI移动信息
        """
        start_time = time.time()
        
        # 简单的AI策略：优先级顺序
        # 1. 如果能获胜，立即获胜
        # 2. 如果对手能获胜，阻止对手
        # 3. 选择评估分数最高的位置
        
        ai_player = Player.WHITE
        human_player = Player.BLACK
        
        # 检查获胜移动
        winning_moves = self.rules.find_winning_moves(self.board, ai_player)
        if winning_moves:
            x, y = winning_moves[0]
            thinking_time = time.time() - start_time
            
            # 执行AI移动
            self.make_move(x, y, 'white')
            
            return {
                'x': x,
                'y': y,
                'player': 'white',
                'thinking_time': thinking_time,
                'strategy': 'winning_move'
            }
        
        # 检查防守移动
        threats = self.rules.find_threats(self.board, ai_player)
        if threats:
            x, y = threats[0]
            thinking_time = time.time() - start_time
            
            # 执行AI移动
            self.make_move(x, y, 'white')
            
            return {
                'x': x,
                'y': y,
                'player': 'white',
                'thinking_time': thinking_time,
                'strategy': 'defensive_move'
            }
        
        # 选择最佳移动
        best_move = self._find_best_move(ai_player)
        if best_move:
            x, y = best_move
            thinking_time = time.time() - start_time
            
            # 执行AI移动
            self.make_move(x, y, 'white')
            
            return {
                'x': x,
                'y': y,
                'player': 'white',
                'thinking_time': thinking_time,
                'strategy': 'best_move'
            }
        
        return None
    
    def _find_best_move(self, player: Player) -> Optional[Tuple[int, int]]:
        """
        找到最佳移动
        
        Args:
            player: 玩家
            
        Returns:
            Optional[Tuple[int, int]]: 最佳移动位置
        """
        best_score = float('-inf')
        best_move = None
        
        # 获取候选移动（优化搜索范围）
        candidate_moves = self.board.get_nearby_moves(2)
        if not candidate_moves:
            candidate_moves = self.board.get_valid_moves()
        
        for x, y in candidate_moves:
            # 临时执行移动
            self.board.make_move(x, y, player)
            
            # 评估位置
            score = self.rules.evaluate_position(self.board, player)
            
            # 撤销移动
            self.board.undo_move()
            
            if score > best_score:
                best_score = score
                best_move = (x, y)
        
        return best_move
    
    def get_hint(self, player_str: str) -> Optional[Dict[str, Any]]:
        """
        获取游戏提示
        
        Args:
            player_str: 玩家字符串
            
        Returns:
            Optional[Dict[str, Any]]: 提示信息
        """
        player = Player.BLACK if player_str == 'black' else Player.WHITE
        
        # 检查获胜移动
        winning_moves = self.rules.find_winning_moves(self.board, player)
        if winning_moves:
            x, y = winning_moves[0]
            return {
                'x': x,
                'y': y,
                'confidence': 1.0,
                'reasoning': '这步棋可以获胜！'
            }
        
        # 检查防守移动
        threats = self.rules.find_threats(self.board, player)
        if threats:
            x, y = threats[0]
            return {
                'x': x,
                'y': y,
                'confidence': 0.9,
                'reasoning': '需要防守对手的威胁'
            }
        
        # 找到最佳移动
        best_move = self._find_best_move(player)
        if best_move:
            x, y = best_move
            return {
                'x': x,
                'y': y,
                'confidence': 0.7,
                'reasoning': '这是当前局面下的最佳选择'
            }
        
        return None
    
    def get_board_state(self) -> List[List[int]]:
        """
        获取棋盘状态
        
        Returns:
            List[List[int]]: 棋盘状态
        """
        return self.board.get_board_state().tolist()
    
    def get_game_info(self) -> Dict[str, Any]:
        """
        获取游戏信息
        
        Returns:
            Dict[str, Any]: 游戏信息
        """
        game = Game.query.get(self.game_id)
        winner = self.board.check_winner()
        
        return {
            'game_id': self.game_id,
            'board_state': self.get_board_state(),
            'current_player': game.current_player,
            'status': game.status,
            'winner': 'black' if winner == Player.BLACK else 'white' if winner == Player.WHITE else None,
            'move_count': len(self.board.move_history),
            'valid_moves': self.board.get_valid_moves()
        }
