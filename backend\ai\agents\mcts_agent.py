"""
基于MCTS的AI代理
"""
import math
import time
import numpy as np
from typing import Dict, List, Tuple, Optional
from game.board import Board, Player
from ai.agents.base_agent import BaseAgent
from ai.model.manager import ModelManager

class MCTSNode:
    """MCTS节点"""
    
    def __init__(self, board: Board, parent: Optional['MCTSNode'] = None, 
                 move: Optional[Tuple[int, int]] = None, prior: float = 0.0):
        self.board = board.copy()
        self.parent = parent
        self.move = move  # 到达此节点的移动
        self.prior = prior  # 先验概率
        
        # MCTS统计
        self.visits = 0
        self.value_sum = 0.0
        self.children: Dict[Tuple[int, int], 'MCTSNode'] = {}
        
        # 状态
        self.is_expanded = False
        self.is_terminal = False
        self.terminal_value = 0.0
    
    def is_leaf(self) -> bool:
        """是否为叶子节点"""
        return not self.is_expanded
    
    def get_value(self) -> float:
        """获取节点平均价值"""
        if self.visits == 0:
            return 0.0
        return self.value_sum / self.visits
    
    def get_ucb_score(self, c_puct: float = 1.0) -> float:
        """计算UCB分数"""
        if self.visits == 0:
            return float('inf')
        
        # UCB公式：Q(s,a) + c_puct * P(s,a) * sqrt(N(s)) / (1 + N(s,a))
        exploration = (c_puct * self.prior * 
                      math.sqrt(self.parent.visits) / (1 + self.visits))
        
        return self.get_value() + exploration
    
    def select_child(self, c_puct: float = 1.0) -> 'MCTSNode':
        """选择最佳子节点"""
        best_score = float('-inf')
        best_child = None
        
        for child in self.children.values():
            score = child.get_ucb_score(c_puct)
            if score > best_score:
                best_score = score
                best_child = child
        
        return best_child
    
    def expand(self, policy_probs: np.ndarray):
        """扩展节点"""
        if self.is_expanded:
            return
        
        valid_moves = self.board.get_valid_moves()
        board_size = self.board.size
        
        for move in valid_moves:
            x, y = move
            # 获取该移动的先验概率
            move_idx = x * board_size + y
            prior = policy_probs[move_idx] if move_idx < len(policy_probs) else 0.0
            
            # 创建子节点
            child_board = self.board.copy()
            child_board.make_move(x, y, self.board.current_player)
            
            child = MCTSNode(child_board, self, move, prior)
            self.children[move] = child
        
        self.is_expanded = True
    
    def backup(self, value: float):
        """回传价值"""
        self.visits += 1
        self.value_sum += value
        
        if self.parent:
            # 从对手角度看，价值需要取反
            self.parent.backup(-value)
    
    def get_action_probs(self, temperature: float = 1.0) -> np.ndarray:
        """获取动作概率分布"""
        board_size = self.board.size
        action_probs = np.zeros(board_size * board_size)
        
        if not self.children:
            return action_probs
        
        # 计算访问次数
        visits = np.array([child.visits for child in self.children.values()])
        moves = list(self.children.keys())
        
        if temperature == 0:
            # 贪婪选择
            best_idx = np.argmax(visits)
            best_move = moves[best_idx]
            action_probs[best_move[0] * board_size + best_move[1]] = 1.0
        else:
            # 根据温度调整概率
            visits = visits ** (1.0 / temperature)
            visits = visits / np.sum(visits)
            
            for i, move in enumerate(moves):
                action_probs[move[0] * board_size + move[1]] = visits[i]
        
        return action_probs

class MCTSAgent(BaseAgent):
    """基于MCTS的AI代理"""
    
    def __init__(self, player: Player, model_manager: ModelManager,
                 simulations: int = 800, c_puct: float = 1.0, temperature: float = 1.0):
        super().__init__(player, "MCTSAgent")
        self.model_manager = model_manager
        self.simulations = simulations
        self.c_puct = c_puct
        self.temperature = temperature
        
    def get_move(self, board: Board) -> Tuple[int, int]:
        """使用MCTS获取最佳移动"""
        start_time = time.time()
        
        # 创建根节点
        root = MCTSNode(board)
        
        # 进行MCTS模拟
        for _ in range(self.simulations):
            self._simulate(root)
        
        # 选择最佳移动
        action_probs = root.get_action_probs(self.temperature)
        board_size = board.size
        
        # 找到概率最高的有效移动
        valid_moves = board.get_valid_moves()
        best_prob = -1
        best_move = None
        
        for move in valid_moves:
            x, y = move
            prob = action_probs[x * board_size + y]
            if prob > best_prob:
                best_prob = prob
                best_move = move
        
        if best_move is None:
            # 如果没有找到，随机选择
            import random
            best_move = random.choice(valid_moves)
        
        # 更新统计
        thinking_time = time.time() - start_time
        self.move_count += 1
        self.total_thinking_time += thinking_time
        
        return best_move
    
    def _simulate(self, root: MCTSNode):
        """执行一次MCTS模拟"""
        node = root
        path = [node]
        
        # 1. 选择阶段：向下选择到叶子节点
        while not node.is_leaf() and not node.is_terminal:
            node = node.select_child(self.c_puct)
            path.append(node)
        
        # 2. 扩展和评估阶段
        value = 0.0
        
        if node.is_terminal:
            # 终端节点，使用真实价值
            value = node.terminal_value
        else:
            # 检查游戏是否结束
            winner = node.board.check_winner()
            if winner is not None:
                node.is_terminal = True
                if winner == self.player:
                    value = 1.0
                elif winner == Player.EMPTY:  # 平局
                    value = 0.0
                else:
                    value = -1.0
                node.terminal_value = value
            else:
                # 使用神经网络评估
                if self.model_manager.current_model is not None:
                    board_state = node.board.get_state_for_ai(self.player)
                    policy_probs, value = self.model_manager.predict(board_state)
                    
                    # 扩展节点
                    node.expand(policy_probs)
                else:
                    # 没有模型时使用随机策略
                    value = 0.0
                    valid_moves = node.board.get_valid_moves()
                    board_size = node.board.size
                    uniform_prob = 1.0 / len(valid_moves) if valid_moves else 0.0
                    
                    policy_probs = np.zeros(board_size * board_size)
                    for move in valid_moves:
                        x, y = move
                        policy_probs[x * board_size + y] = uniform_prob
                    
                    node.expand(policy_probs)
        
        # 3. 回传阶段
        for node in reversed(path):
            node.backup(value)
            value = -value  # 切换视角
    
    def get_action_probs(self, board: Board, temperature: float = 1.0) -> np.ndarray:
        """获取动作概率分布（用于训练数据生成）"""
        # 创建根节点并进行MCTS
        root = MCTSNode(board)
        
        for _ in range(self.simulations):
            self._simulate(root)
        
        return root.get_action_probs(temperature)
