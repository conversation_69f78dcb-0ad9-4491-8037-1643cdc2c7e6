"""
训练相关WebSocket事件处理
"""
from flask_socketio import emit, join_room, leave_room
from app import socketio
import logging

logger = logging.getLogger(__name__)

@socketio.on('join_training_monitor')
def on_join_training_monitor():
    """加入训练监控房间"""
    try:
        join_room('training_monitor')
        emit('training_monitor_joined', {
            'status': 'success',
            'message': 'Joined training monitor'
        })
        logger.info(f'Client {request.sid} joined training monitor')
        
    except Exception as e:
        logger.error(f'Error joining training monitor: {str(e)}')
        emit('error', {'message': 'Failed to join training monitor'})

@socketio.on('leave_training_monitor')
def on_leave_training_monitor():
    """离开训练监控房间"""
    try:
        leave_room('training_monitor')
        logger.info(f'Client {request.sid} left training monitor')
        
    except Exception as e:
        logger.error(f'Error leaving training monitor: {str(e)}')

def broadcast_training_update(data):
    """广播训练更新到所有监控客户端"""
    socketio.emit('training_update', data, room='training_monitor')

def broadcast_training_started(session_id, config):
    """广播训练开始事件"""
    socketio.emit('training_started', {
        'session_id': session_id,
        'config': {
            'epochs': config.epochs,
            'batch_size': config.batch_size,
            'learning_rate': config.learning_rate,
            'model_name': config.model_name
        }
    }, room='training_monitor')

def broadcast_training_progress(session_id, progress):
    """广播训练进度"""
    socketio.emit('training_progress', {
        'session_id': session_id,
        'current_epoch': progress.get('current_epoch'),
        'total_epochs': progress.get('total_epochs'),
        'current_loss': progress.get('current_loss'),
        'win_rate': progress.get('win_rate'),
        'games_played': progress.get('games_played'),
        'elapsed_time': progress.get('elapsed_time'),
        'estimated_remaining': progress.get('estimated_remaining')
    }, room='training_monitor')

def broadcast_training_completed(session_id, results):
    """广播训练完成事件"""
    socketio.emit('training_completed', {
        'session_id': session_id,
        'final_loss': results.get('final_loss'),
        'final_win_rate': results.get('final_win_rate'),
        'total_epochs': results.get('total_epochs'),
        'model_path': results.get('model_path')
    }, room='training_monitor')

def broadcast_training_error(session_id, error_message):
    """广播训练错误事件"""
    socketio.emit('training_error', {
        'session_id': session_id,
        'error': error_message
    }, room='training_monitor')

def broadcast_self_play_game(game_data):
    """广播自我对弈游戏数据"""
    socketio.emit('self_play_game', {
        'game_id': game_data.get('game_id'),
        'moves': game_data.get('moves'),
        'winner': game_data.get('winner'),
        'game_length': game_data.get('game_length'),
        'evaluation_score': game_data.get('evaluation_score')
    }, room='training_monitor')
