"""
应用配置文件
"""
import os
from pathlib import Path

# 项目根目录
BASE_DIR = Path(__file__).parent.parent

class Config:
    """基础配置"""
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f'sqlite:///{BASE_DIR}/data/app.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # WebSocket配置
    SOCKETIO_ASYNC_MODE = 'eventlet'
    
    # AI模型配置
    MODEL_DIR = BASE_DIR / 'data' / 'models'
    TRAINING_DATA_DIR = BASE_DIR / 'data' / 'training'
    GAME_DATA_DIR = BASE_DIR / 'data' / 'games'
    
    # 游戏配置
    BOARD_SIZE = 15
    WIN_LENGTH = 5
    
    # 训练配置
    BATCH_SIZE = 32
    LEARNING_RATE = 0.001
    EPOCHS = 1000
    SAVE_INTERVAL = 100
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = BASE_DIR / 'logs' / 'app.log'

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
