"""
Flask应用启动文件
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app import create_app, socketio
from loguru import logger

def setup_logging():
    """设置日志配置"""
    log_dir = project_root / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    # 配置loguru
    logger.add(
        log_dir / 'app.log',
        rotation='1 day',
        retention='30 days',
        level='INFO',
        format='{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}'
    )

def main():
    """主函数"""
    setup_logging()
    
    # 创建Flask应用
    app = create_app()
    
    # 获取配置
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 5000))
    debug = app.config.get('DEBUG', False)
    
    logger.info(f'Starting AI Gomoku System on {host}:{port}')
    logger.info(f'Debug mode: {debug}')
    
    # 启动应用
    socketio.run(
        app,
        host=host,
        port=port,
        debug=debug,
        allow_unsafe_werkzeug=True
    )

if __name__ == '__main__':
    main()
