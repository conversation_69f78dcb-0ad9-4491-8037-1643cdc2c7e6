/**
 * 主应用入口文件
 */

class GomokuApp {
    constructor() {
        this.gameManager = null;
        this.isInitialized = false;
        
        this.init();
    }
    
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.initializeApp();
            });
        } else {
            this.initializeApp();
        }
    }
    
    initializeApp() {
        try {
            console.log('Initializing Gomoku App...');
            
            // 初始化各个管理器
            this.setupErrorHandling();
            this.loadUserPreferences();
            this.initializeGameManager();
            this.setupPerformanceMonitoring();
            
            // 标记为已初始化
            this.isInitialized = true;
            
            console.log('Gomoku App initialized successfully');
            
            // 显示欢迎消息
            this.showWelcomeMessage();
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.handleInitializationError(error);
        }
    }
    
    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.handleError(event.error);
        });
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.handleError(event.reason);
        });
        
        // WebSocket错误处理
        wsManager.on('error', (error) => {
            console.error('WebSocket error:', error);
            uiManager.showToast('连接错误: ' + error.message, 'error');
        });
        
        wsManager.on('disconnected', (reason) => {
            console.warn('WebSocket disconnected:', reason);
            uiManager.showToast('连接已断开，正在尝试重连...', 'warning');
        });
        
        wsManager.on('connected', () => {
            console.log('WebSocket connected');
            uiManager.showToast('连接已建立', 'success', 2000);
        });
    }
    
    loadUserPreferences() {
        // 加载用户偏好设置
        const preferences = uiManager.loadFromStorage(CONFIG.STORAGE_KEYS.USER_PREFERENCES, {
            theme: 'light',
            soundEnabled: true,
            animationsEnabled: true,
            autoSave: true
        });
        
        // 应用主题
        uiManager.setTheme(preferences.theme);
        
        // 应用其他设置
        this.applyPreferences(preferences);
        
        console.log('User preferences loaded:', preferences);
    }
    
    applyPreferences(preferences) {
        // 应用音效设置
        CONFIG.SOUND.ENABLED = preferences.soundEnabled;
        
        // 应用动画设置
        if (!preferences.animationsEnabled) {
            document.documentElement.style.setProperty('--animation-duration', '0s');
        }
        
        // 其他偏好设置...
    }
    
    initializeGameManager() {
        // 创建游戏管理器
        this.gameManager = new GameManager();
        
        // 设置游戏事件监听
        this.setupGameEventHandlers();
        
        console.log('Game manager initialized');
    }
    
    setupGameEventHandlers() {
        // 监听游戏状态变化
        // 这里可以添加自定义事件监听
    }
    
    setupPerformanceMonitoring() {
        if (!CONFIG.DEBUG.PERFORMANCE_MONITORING) {
            return;
        }
        
        // 监控性能指标
        if ('performance' in window) {
            // 页面加载时间
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                }, 0);
            });
            
            // 内存使用情况（如果支持）
            if ('memory' in performance) {
                setInterval(() => {
                    const memory = performance.memory;
                    console.log('Memory usage:', {
                        used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
                        total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
                        limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB'
                    });
                }, 30000); // 每30秒记录一次
            }
        }
    }
    
    showWelcomeMessage() {
        // 检查是否是首次访问
        const isFirstVisit = !uiManager.loadFromStorage('hasVisited', false);
        
        if (isFirstVisit) {
            setTimeout(() => {
                uiManager.showToast('欢迎使用AI五子棋系统！', 'info', 5000);
                uiManager.saveToStorage('hasVisited', true);
            }, 1000);
        }
    }
    
    handleInitializationError(error) {
        // 显示初始化错误
        const errorMessage = document.createElement('div');
        errorMessage.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #e74c3c;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 10000;
            max-width: 400px;
        `;
        errorMessage.innerHTML = `
            <h3>初始化失败</h3>
            <p>应用程序初始化时发生错误，请刷新页面重试。</p>
            <button onclick="location.reload()" style="
                background: white;
                color: #e74c3c;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin-top: 10px;
            ">刷新页面</button>
        `;
        
        document.body.appendChild(errorMessage);
    }
    
    handleError(error) {
        // 根据错误类型显示不同的处理方式
        if (error.name === 'NetworkError') {
            uiManager.showToast('网络连接错误，请检查网络设置', 'error');
        } else if (error.name === 'TypeError') {
            console.error('Type error:', error);
            // 类型错误通常是代码问题，不向用户显示
        } else {
            uiManager.showToast('发生未知错误，请刷新页面重试', 'error');
        }
    }
    
    // 应用生命周期方法
    onBeforeUnload() {
        // 页面卸载前的清理工作
        if (this.gameManager && this.gameManager.gameState === 'playing') {
            // 保存游戏状态
            this.saveGameState();
        }
        
        // 断开WebSocket连接
        if (wsManager) {
            wsManager.disconnect();
        }
    }
    
    saveGameState() {
        if (!this.gameManager) return;
        
        const gameState = {
            gameId: this.gameManager.gameId,
            boardState: this.gameManager.board ? this.gameManager.board.getBoardState() : null,
            moveHistory: this.gameManager.board ? this.gameManager.board.getMoveHistory() : [],
            currentPlayer: this.gameManager.currentPlayer,
            moveCount: this.gameManager.moveCount,
            gameStartTime: this.gameManager.gameStartTime,
            playerType: this.gameManager.playerType,
            difficulty: this.gameManager.difficulty
        };
        
        uiManager.saveToStorage(CONFIG.STORAGE_KEYS.GAME_HISTORY, gameState);
        console.log('Game state saved');
    }
    
    loadGameState() {
        const savedState = uiManager.loadFromStorage(CONFIG.STORAGE_KEYS.GAME_HISTORY);
        
        if (savedState && savedState.gameId) {
            // 询问用户是否恢复游戏
            uiManager.showConfirm(
                '检测到未完成的游戏，是否恢复？',
                () => {
                    this.restoreGameState(savedState);
                },
                () => {
                    uiManager.removeFromStorage(CONFIG.STORAGE_KEYS.GAME_HISTORY);
                }
            );
        }
    }
    
    restoreGameState(savedState) {
        if (!this.gameManager) return;
        
        try {
            // 恢复游戏状态
            this.gameManager.gameId = savedState.gameId;
            this.gameManager.currentPlayer = savedState.currentPlayer;
            this.gameManager.moveCount = savedState.moveCount;
            this.gameManager.gameStartTime = savedState.gameStartTime;
            this.gameManager.playerType = savedState.playerType;
            this.gameManager.difficulty = savedState.difficulty;
            
            // 恢复棋盘状态
            if (savedState.boardState && this.gameManager.board) {
                this.gameManager.board.setBoardState(savedState.boardState, savedState.moveHistory);
            }
            
            // 重新加入游戏房间
            wsManager.joinGame(savedState.gameId);
            
            uiManager.showToast('游戏状态已恢复', 'success');
            
        } catch (error) {
            console.error('Failed to restore game state:', error);
            uiManager.showToast('恢复游戏状态失败', 'error');
            uiManager.removeFromStorage(CONFIG.STORAGE_KEYS.GAME_HISTORY);
        }
    }
    
    // 调试方法
    getDebugInfo() {
        return {
            isInitialized: this.isInitialized,
            gameState: this.gameManager ? this.gameManager.gameState : null,
            wsConnected: wsManager ? wsManager.isSocketConnected() : false,
            config: CONFIG,
            performance: performance.now()
        };
    }
}

// 页面卸载前保存状态
window.addEventListener('beforeunload', (event) => {
    if (window.gomokuApp) {
        window.gomokuApp.onBeforeUnload();
    }
});

// 页面可见性变化处理
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        // 页面隐藏时暂停一些操作
        console.log('Page hidden');
    } else {
        // 页面显示时恢复操作
        console.log('Page visible');
        
        // 检查WebSocket连接状态
        if (wsManager && !wsManager.isSocketConnected()) {
            wsManager.reconnect();
        }
    }
});

// 创建全局应用实例
window.gomokuApp = new GomokuApp();

// 开发模式下暴露调试接口
if (CONFIG.DEBUG.ENABLED) {
    window.debug = {
        app: window.gomokuApp,
        wsManager: wsManager,
        uiManager: uiManager,
        config: CONFIG,
        getDebugInfo: () => window.gomokuApp.getDebugInfo()
    };
    
    console.log('Debug mode enabled. Use window.debug to access debug interfaces.');
}
