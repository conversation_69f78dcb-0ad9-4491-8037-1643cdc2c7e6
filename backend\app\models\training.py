"""
训练相关数据模型
"""
from app import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean, JSON

class TrainingConfig(db.Model):
    """训练配置表"""
    __tablename__ = 'training_configs'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # 训练参数
    epochs = Column(Integer, default=1000)
    batch_size = Column(Integer, default=32)
    learning_rate = Column(Float, default=0.001)
    self_play_games = Column(Integer, default=100)
    
    # 模型参数
    model_name = Column(String(100), nullable=False)
    model_architecture = Column(String(50), default='resnet')  # 'resnet', 'cnn', 'transformer'
    hidden_size = Column(Integer, default=256)
    num_layers = Column(Integer, default=10)
    
    # 强化学习参数
    exploration_rate = Column(Float, default=0.1)
    discount_factor = Column(Float, default=0.99)
    temperature = Column(Float, default=1.0)
    
    # 其他配置
    use_gpu = Column(Boolean, default=True)
    save_interval = Column(Integer, default=100)
    evaluation_interval = Column(Integer, default=50)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    sessions = db.relationship('TrainingSession', backref='config', lazy='dynamic')
    
    def __repr__(self):
        return f'<TrainingConfig {self.id}: {self.name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'epochs': self.epochs,
            'batch_size': self.batch_size,
            'learning_rate': self.learning_rate,
            'self_play_games': self.self_play_games,
            'model_name': self.model_name,
            'model_architecture': self.model_architecture,
            'hidden_size': self.hidden_size,
            'num_layers': self.num_layers,
            'exploration_rate': self.exploration_rate,
            'discount_factor': self.discount_factor,
            'temperature': self.temperature,
            'use_gpu': self.use_gpu,
            'save_interval': self.save_interval,
            'evaluation_interval': self.evaluation_interval,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class TrainingSession(db.Model):
    """训练会话表"""
    __tablename__ = 'training_sessions'
    
    id = Column(Integer, primary_key=True)
    config_id = Column(Integer, db.ForeignKey('training_configs.id'), nullable=False)
    
    # 会话状态
    status = Column(String(20), default='running')  # 'running', 'completed', 'failed', 'stopped'
    
    # 时间信息
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # 训练进度
    current_epoch = Column(Integer, default=0)
    total_epochs = Column(Integer)
    
    # 训练结果
    final_loss = Column(Float)
    final_win_rate = Column(Float)
    best_win_rate = Column(Float)
    best_epoch = Column(Integer)
    
    # 统计信息
    total_games_played = Column(Integer, default=0)
    total_training_time = Column(Float)  # 总训练时间（秒）
    
    # 错误信息
    error_message = Column(Text)
    
    # 关联关系
    metrics = db.relationship('TrainingMetric', backref='session', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<TrainingSession {self.id}: {self.status}, Epoch {self.current_epoch}/{self.total_epochs}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'config_id': self.config_id,
            'status': self.status,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'current_epoch': self.current_epoch,
            'total_epochs': self.total_epochs,
            'final_loss': self.final_loss,
            'final_win_rate': self.final_win_rate,
            'best_win_rate': self.best_win_rate,
            'best_epoch': self.best_epoch,
            'total_games_played': self.total_games_played,
            'total_training_time': self.total_training_time,
            'error_message': self.error_message
        }

class TrainingMetric(db.Model):
    """训练指标表"""
    __tablename__ = 'training_metrics'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, db.ForeignKey('training_sessions.id'), nullable=False)
    
    # 训练指标
    epoch = Column(Integer, nullable=False)
    loss = Column(Float)
    policy_loss = Column(Float)
    value_loss = Column(Float)
    
    # 评估指标
    win_rate = Column(Float)
    draw_rate = Column(Float)
    avg_game_length = Column(Float)
    
    # 学习率和其他参数
    learning_rate = Column(Float)
    exploration_rate = Column(Float)
    
    # 时间戳
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        db.Index('idx_session_epoch', 'session_id', 'epoch'),
    )
    
    def __repr__(self):
        return f'<TrainingMetric {self.id}: Session {self.session_id}, Epoch {self.epoch}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'epoch': self.epoch,
            'loss': self.loss,
            'policy_loss': self.policy_loss,
            'value_loss': self.value_loss,
            'win_rate': self.win_rate,
            'draw_rate': self.draw_rate,
            'avg_game_length': self.avg_game_length,
            'learning_rate': self.learning_rate,
            'exploration_rate': self.exploration_rate,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }

class SelfPlayGame(db.Model):
    """自我对弈游戏表"""
    __tablename__ = 'self_play_games'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, db.ForeignKey('training_sessions.id'), nullable=False)
    
    # 游戏信息
    game_data = Column(JSON)  # 存储完整的游戏数据
    winner = Column(String(10))  # 'black', 'white', 'draw'
    game_length = Column(Integer)  # 游戏长度（步数）
    
    # 评估分数
    final_evaluation = Column(Float)
    avg_move_time = Column(Float)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<SelfPlayGame {self.id}: Session {self.session_id}, Winner: {self.winner}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'game_data': self.game_data,
            'winner': self.winner,
            'game_length': self.game_length,
            'final_evaluation': self.final_evaluation,
            'avg_move_time': self.avg_move_time,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
