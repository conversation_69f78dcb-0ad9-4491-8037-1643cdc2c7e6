/**
 * D3.js棋盘绘制和交互管理
 */

class GameBoard {
    constructor(containerId) {
        this.containerId = containerId;
        this.svg = null;
        this.boardGroup = null;
        this.stonesGroup = null;
        this.overlayGroup = null;
        
        this.boardSize = CONFIG.BOARD.SIZE;
        this.cellSize = CONFIG.BOARD.CELL_SIZE;
        this.margin = CONFIG.BOARD.MARGIN;
        this.stoneRadius = CONFIG.STONE.RADIUS;
        
        this.boardState = Array(this.boardSize).fill().map(() => Array(this.boardSize).fill(0));
        this.moveHistory = [];
        this.isInteractive = true;
        this.currentPlayer = 1; // 1: 黑棋, 2: 白棋
        
        this.onCellClick = null;
        this.onCellHover = null;
        
        this.init();
    }
    
    init() {
        this.createSVG();
        this.createGradients();
        this.drawBoard();
        this.setupInteraction();
    }
    
    createSVG() {
        const totalSize = (this.boardSize - 1) * this.cellSize + 2 * this.margin;
        
        this.svg = d3.select(`#${this.containerId}`)
            .attr('width', totalSize)
            .attr('height', totalSize)
            .attr('viewBox', `0 0 ${totalSize} ${totalSize}`);
        
        // 创建分组
        this.boardGroup = this.svg.append('g').attr('class', 'board-group');
        this.stonesGroup = this.svg.append('g').attr('class', 'stones-group');
        this.overlayGroup = this.svg.append('g').attr('class', 'overlay-group');
    }
    
    createGradients() {
        const defs = this.svg.append('defs');
        
        // 黑棋渐变
        const blackGradient = defs.append('radialGradient')
            .attr('id', 'blackGradient')
            .attr('cx', '30%')
            .attr('cy', '30%');
        
        blackGradient.append('stop')
            .attr('offset', '0%')
            .attr('stop-color', '#666');
        
        blackGradient.append('stop')
            .attr('offset', '100%')
            .attr('stop-color', '#000');
        
        // 白棋渐变
        const whiteGradient = defs.append('radialGradient')
            .attr('id', 'whiteGradient')
            .attr('cx', '30%')
            .attr('cy', '30%');
        
        whiteGradient.append('stop')
            .attr('offset', '0%')
            .attr('stop-color', '#fff');
        
        whiteGradient.append('stop')
            .attr('offset', '100%')
            .attr('stop-color', '#ddd');
    }
    
    drawBoard() {
        // 绘制棋盘背景
        this.boardGroup.append('rect')
            .attr('x', 0)
            .attr('y', 0)
            .attr('width', (this.boardSize - 1) * this.cellSize + 2 * this.margin)
            .attr('height', (this.boardSize - 1) * this.cellSize + 2 * this.margin)
            .attr('fill', CONFIG.BOARD.BACKGROUND_COLOR)
            .attr('rx', 10);
        
        // 绘制网格线
        this.drawGridLines();
        
        // 绘制星位点
        this.drawStarPoints();
        
        // 绘制坐标标签
        this.drawCoordinateLabels();
    }
    
    drawGridLines() {
        const linesGroup = this.boardGroup.append('g').attr('class', 'grid-lines');
        
        // 垂直线
        for (let i = 0; i < this.boardSize; i++) {
            linesGroup.append('line')
                .attr('x1', this.margin + i * this.cellSize)
                .attr('y1', this.margin)
                .attr('x2', this.margin + i * this.cellSize)
                .attr('y2', this.margin + (this.boardSize - 1) * this.cellSize)
                .attr('class', 'board-line');
        }
        
        // 水平线
        for (let i = 0; i < this.boardSize; i++) {
            linesGroup.append('line')
                .attr('x1', this.margin)
                .attr('y1', this.margin + i * this.cellSize)
                .attr('x2', this.margin + (this.boardSize - 1) * this.cellSize)
                .attr('y2', this.margin + i * this.cellSize)
                .attr('class', 'board-line');
        }
    }
    
    drawStarPoints() {
        const starPoints = [
            [3, 3], [3, 11], [7, 7], [11, 3], [11, 11]
        ];
        
        const starsGroup = this.boardGroup.append('g').attr('class', 'star-points');
        
        starPoints.forEach(([x, y]) => {
            starsGroup.append('circle')
                .attr('cx', this.margin + x * this.cellSize)
                .attr('cy', this.margin + y * this.cellSize)
                .attr('r', 3)
                .attr('fill', CONFIG.BOARD.LINE_COLOR);
        });
    }
    
    drawCoordinateLabels() {
        if (!CONFIG.DEBUG.SHOW_COORDINATES) return;
        
        const labelsGroup = this.boardGroup.append('g').attr('class', 'coordinate-labels');
        
        // 数字标签 (1-15)
        for (let i = 0; i < this.boardSize; i++) {
            labelsGroup.append('text')
                .attr('x', this.margin / 2)
                .attr('y', this.margin + i * this.cellSize + 5)
                .attr('text-anchor', 'middle')
                .attr('font-size', '12px')
                .attr('fill', '#666')
                .text(this.boardSize - i);
        }
        
        // 字母标签 (A-O)
        for (let i = 0; i < this.boardSize; i++) {
            labelsGroup.append('text')
                .attr('x', this.margin + i * this.cellSize)
                .attr('y', this.margin / 2 + 5)
                .attr('text-anchor', 'middle')
                .attr('font-size', '12px')
                .attr('fill', '#666')
                .text(String.fromCharCode(65 + i));
        }
    }
    
    setupInteraction() {
        // 创建交互区域
        const interactionGroup = this.overlayGroup.append('g').attr('class', 'interaction-group');
        
        for (let x = 0; x < this.boardSize; x++) {
            for (let y = 0; y < this.boardSize; y++) {
                const circle = interactionGroup.append('circle')
                    .attr('cx', this.margin + x * this.cellSize)
                    .attr('cy', this.margin + y * this.cellSize)
                    .attr('r', this.stoneRadius)
                    .attr('class', 'board-intersection')
                    .attr('data-x', x)
                    .attr('data-y', y);
                
                // 点击事件
                circle.on('click', (event) => {
                    if (!this.isInteractive || this.boardState[x][y] !== 0) return;
                    
                    if (this.onCellClick) {
                        this.onCellClick(x, y);
                    }
                });
                
                // 悬停事件
                circle.on('mouseenter', (event) => {
                    if (!this.isInteractive || this.boardState[x][y] !== 0) return;
                    
                    this.showHoverStone(x, y);
                    
                    if (this.onCellHover) {
                        this.onCellHover(x, y, true);
                    }
                });
                
                circle.on('mouseleave', (event) => {
                    this.hideHoverStone();
                    
                    if (this.onCellHover) {
                        this.onCellHover(x, y, false);
                    }
                });
            }
        }
    }
    
    showHoverStone(x, y) {
        this.hideHoverStone();
        
        const color = this.currentPlayer === 1 ? 'url(#blackGradient)' : 'url(#whiteGradient)';
        
        this.overlayGroup.append('circle')
            .attr('class', 'hover-stone')
            .attr('cx', this.margin + x * this.cellSize)
            .attr('cy', this.margin + y * this.cellSize)
            .attr('r', this.stoneRadius)
            .attr('fill', color)
            .attr('opacity', 0.6)
            .attr('stroke', CONFIG.STONE.BORDER_COLOR)
            .attr('stroke-width', 1);
    }
    
    hideHoverStone() {
        this.overlayGroup.selectAll('.hover-stone').remove();
    }
    
    placeStone(x, y, player, animate = true) {
        if (this.boardState[x][y] !== 0) {
            console.warn(`Position (${x}, ${y}) is already occupied`);
            return false;
        }
        
        this.boardState[x][y] = player;
        this.moveHistory.push({ x, y, player });
        
        const color = player === 1 ? 'url(#blackGradient)' : 'url(#whiteGradient)';
        const className = player === 1 ? 'black-game-stone' : 'white-game-stone';
        
        const stone = this.stonesGroup.append('circle')
            .attr('cx', this.margin + x * this.cellSize)
            .attr('cy', this.margin + y * this.cellSize)
            .attr('r', animate ? 0 : this.stoneRadius)
            .attr('class', `board-stone ${className}`)
            .attr('fill', color)
            .attr('stroke', CONFIG.STONE.BORDER_COLOR)
            .attr('stroke-width', CONFIG.STONE.BORDER_WIDTH)
            .attr('data-x', x)
            .attr('data-y', y)
            .attr('data-move', this.moveHistory.length);
        
        if (animate) {
            stone.transition()
                .duration(CONFIG.ANIMATION.STONE_PLACE_DURATION)
                .attr('r', this.stoneRadius)
                .ease(d3.easeElastic);
        }
        
        // 标记最后一步
        this.markLastMove();
        
        // 显示移动编号（调试模式）
        if (CONFIG.DEBUG.SHOW_MOVE_NUMBERS) {
            this.stonesGroup.append('text')
                .attr('x', this.margin + x * this.cellSize)
                .attr('y', this.margin + y * this.cellSize + 4)
                .attr('text-anchor', 'middle')
                .attr('font-size', '10px')
                .attr('fill', player === 1 ? 'white' : 'black')
                .attr('pointer-events', 'none')
                .text(this.moveHistory.length);
        }
        
        return true;
    }
    
    markLastMove() {
        // 移除之前的标记
        this.stonesGroup.selectAll('.last-move').classed('last-move', false);
        
        // 标记最后一步
        if (this.moveHistory.length > 0) {
            const lastMove = this.moveHistory[this.moveHistory.length - 1];
            this.stonesGroup.selectAll('.board-stone')
                .filter(function() {
                    const stone = d3.select(this);
                    return stone.attr('data-x') == lastMove.x && stone.attr('data-y') == lastMove.y;
                })
                .classed('last-move', true);
        }
    }
    
    highlightWinningStones(positions) {
        positions.forEach(([x, y]) => {
            this.stonesGroup.selectAll('.board-stone')
                .filter(function() {
                    const stone = d3.select(this);
                    return stone.attr('data-x') == x && stone.attr('data-y') == y;
                })
                .classed('winning-stone', true);
        });
    }
    
    showHint(x, y, confidence = 1.0) {
        this.hideHint();
        
        this.overlayGroup.append('circle')
            .attr('class', 'hint-indicator')
            .attr('cx', this.margin + x * this.cellSize)
            .attr('cy', this.margin + y * this.cellSize)
            .attr('r', this.stoneRadius + 3)
            .attr('fill', 'none')
            .attr('stroke', '#27ae60')
            .attr('stroke-width', 3)
            .attr('opacity', confidence);
    }
    
    hideHint() {
        this.overlayGroup.selectAll('.hint-indicator').remove();
    }
    
    clearBoard() {
        this.boardState = Array(this.boardSize).fill().map(() => Array(this.boardSize).fill(0));
        this.moveHistory = [];
        this.stonesGroup.selectAll('.board-stone').remove();
        this.overlayGroup.selectAll('*').remove();
        this.setupInteraction();
    }
    
    setBoardState(newState, moveHistory = []) {
        this.clearBoard();
        this.moveHistory = [...moveHistory];
        
        for (let x = 0; x < this.boardSize; x++) {
            for (let y = 0; y < this.boardSize; y++) {
                if (newState[x][y] !== 0) {
                    this.placeStone(x, y, newState[x][y], false);
                }
            }
        }
    }
    
    setInteractive(interactive) {
        this.isInteractive = interactive;
        this.overlayGroup.style('pointer-events', interactive ? 'auto' : 'none');
    }
    
    setCurrentPlayer(player) {
        this.currentPlayer = player;
    }
    
    getBoardState() {
        return this.boardState.map(row => [...row]);
    }
    
    getMoveHistory() {
        return [...this.moveHistory];
    }
    
    getLastMove() {
        return this.moveHistory.length > 0 ? this.moveHistory[this.moveHistory.length - 1] : null;
    }
    
    // 坐标转换工具
    boardToPixel(x, y) {
        return {
            x: this.margin + x * this.cellSize,
            y: this.margin + y * this.cellSize
        };
    }
    
    pixelToBoard(pixelX, pixelY) {
        const x = Math.round((pixelX - this.margin) / this.cellSize);
        const y = Math.round((pixelY - this.margin) / this.cellSize);
        
        if (x >= 0 && x < this.boardSize && y >= 0 && y < this.boardSize) {
            return { x, y };
        }
        
        return null;
    }
}
