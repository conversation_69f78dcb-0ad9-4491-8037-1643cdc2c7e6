/**
 * UI工具函数和组件
 */

class UIManager {
    constructor() {
        this.toasts = [];
        this.modals = new Map();
        
        this.init();
    }
    
    init() {
        this.createToastContainer();
        this.setupModalHandlers();
        this.setupKeyboardShortcuts();
    }
    
    createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            pointer-events: none;
        `;
        document.body.appendChild(container);
    }
    
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            background: ${this.getToastColor(type)};
            color: white;
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            pointer-events: auto;
            cursor: pointer;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        toast.textContent = message;
        
        // 点击关闭
        toast.addEventListener('click', () => {
            this.hideToast(toast);
        });
        
        const container = document.getElementById('toastContainer');
        container.appendChild(toast);
        
        // 动画显示
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                this.hideToast(toast);
            }, duration);
        }
        
        this.toasts.push(toast);
        return toast;
    }
    
    hideToast(toast) {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
            const index = this.toasts.indexOf(toast);
            if (index > -1) {
                this.toasts.splice(index, 1);
            }
        }, 300);
    }
    
    getToastColor(type) {
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            warning: '#f39c12',
            info: '#3498db'
        };
        return colors[type] || colors.info;
    }
    
    setupModalHandlers() {
        // 点击背景关闭模态框
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
        
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const visibleModals = document.querySelectorAll('.modal[style*="block"]');
                visibleModals.forEach(modal => {
                    modal.style.display = 'none';
                });
            }
        });
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + N: 新游戏
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                const newGameBtn = document.getElementById('newGameBtn');
                if (newGameBtn && !newGameBtn.disabled) {
                    newGameBtn.click();
                }
            }
            
            // H: 提示
            if (e.key === 'h' || e.key === 'H') {
                const hintBtn = document.getElementById('hintBtn');
                if (hintBtn && !hintBtn.disabled) {
                    hintBtn.click();
                }
            }
            
            // S: 投降
            if (e.key === 's' || e.key === 'S') {
                const surrenderBtn = document.getElementById('surrenderBtn');
                if (surrenderBtn && !surrenderBtn.disabled) {
                    surrenderBtn.click();
                }
            }
            
            // U: 悔棋
            if (e.key === 'u' || e.key === 'U') {
                const undoBtn = document.getElementById('undoBtn');
                if (undoBtn && !undoBtn.disabled) {
                    undoBtn.click();
                }
            }
        });
    }
    
    showConfirm(message, onConfirm, onCancel) {
        const modal = this.createConfirmModal(message, onConfirm, onCancel);
        document.body.appendChild(modal);
        modal.style.display = 'block';
        
        // 聚焦到确认按钮
        const confirmBtn = modal.querySelector('.confirm-btn');
        if (confirmBtn) {
            confirmBtn.focus();
        }
        
        return modal;
    }
    
    createConfirmModal(message, onConfirm, onCancel) {
        const modal = document.createElement('div');
        modal.className = 'modal confirm-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>确认</h3>
                <p>${message}</p>
                <div class="modal-actions">
                    <button class="btn btn-primary confirm-btn">确认</button>
                    <button class="btn btn-secondary cancel-btn">取消</button>
                </div>
            </div>
        `;
        
        const confirmBtn = modal.querySelector('.confirm-btn');
        const cancelBtn = modal.querySelector('.cancel-btn');
        
        confirmBtn.addEventListener('click', () => {
            modal.style.display = 'none';
            document.body.removeChild(modal);
            if (onConfirm) onConfirm();
        });
        
        cancelBtn.addEventListener('click', () => {
            modal.style.display = 'none';
            document.body.removeChild(modal);
            if (onCancel) onCancel();
        });
        
        return modal;
    }
    
    showProgress(title, message = '') {
        const modal = this.createProgressModal(title, message);
        document.body.appendChild(modal);
        modal.style.display = 'block';
        
        return {
            modal: modal,
            updateProgress: (percent, message) => {
                const progressBar = modal.querySelector('.progress-fill');
                const progressText = modal.querySelector('.progress-text');
                
                if (progressBar) {
                    progressBar.style.width = `${percent}%`;
                }
                if (progressText && message) {
                    progressText.textContent = message;
                }
            },
            close: () => {
                modal.style.display = 'none';
                if (modal.parentNode) {
                    document.body.removeChild(modal);
                }
            }
        };
    }
    
    createProgressModal(title, message) {
        const modal = document.createElement('div');
        modal.className = 'modal progress-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>${title}</h3>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">${message}</div>
                </div>
            </div>
        `;
        
        return modal;
    }
    
    animateNumber(element, start, end, duration = 1000) {
        const startTime = performance.now();
        const difference = end - start;
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeProgress = this.easeOutCubic(progress);
            const current = start + (difference * easeProgress);
            
            element.textContent = Math.round(current);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
    
    formatFileSize(bytes) {
        const sizes = ['B', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 B';
        
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    
    copyToClipboard(text) {
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(text).then(() => {
                this.showToast('已复制到剪贴板', 'success');
            }).catch(() => {
                this.fallbackCopyToClipboard(text);
            });
        } else {
            this.fallbackCopyToClipboard(text);
        }
    }
    
    fallbackCopyToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.showToast('已复制到剪贴板', 'success');
        } catch (err) {
            this.showToast('复制失败', 'error');
        }
        
        document.body.removeChild(textArea);
    }
    
    downloadFile(data, filename, type = 'application/json') {
        const blob = new Blob([data], { type });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }
    
    // 响应式工具
    isMobile() {
        return window.innerWidth <= 768;
    }
    
    isTablet() {
        return window.innerWidth > 768 && window.innerWidth <= 1024;
    }
    
    isDesktop() {
        return window.innerWidth > 1024;
    }
    
    // 主题切换
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
    }
    
    getTheme() {
        return localStorage.getItem('theme') || 'light';
    }
    
    // 本地存储工具
    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
            return false;
        }
    }
    
    loadFromStorage(key, defaultValue = null) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (error) {
            console.error('Failed to load from localStorage:', error);
            return defaultValue;
        }
    }
    
    removeFromStorage(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Failed to remove from localStorage:', error);
            return false;
        }
    }
}

// 创建全局UI管理器实例
const uiManager = new UIManager();
