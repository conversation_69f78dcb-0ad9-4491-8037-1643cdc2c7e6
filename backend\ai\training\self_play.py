"""
自我对弈工作器
"""
import time
import numpy as np
from typing import List, Tuple, Dict, Any
from game.board import Board, Player
from ai.agents.mcts_agent import MCTSAgent
from ai.training.experience import ExperienceBuffer
from ai.model.manager import ModelManager

class SelfPlayWorker:
    """自我对弈工作器"""
    
    def __init__(self, model_manager: ModelManager, experience_buffer: ExperienceBuffer,
                 simulations: int = 800, temperature: float = 1.0, 
                 temperature_threshold: int = 30):
        self.model_manager = model_manager
        self.experience_buffer = experience_buffer
        self.simulations = simulations
        self.temperature = temperature
        self.temperature_threshold = temperature_threshold
        
        # 创建MCTS代理
        self.agent_black = MCTSAgent(Player.BLACK, model_manager, simulations, temperature=temperature)
        self.agent_white = MCTSAgent(Player.WHITE, model_manager, simulations, temperature=temperature)
    
    def play_game(self, game_id: int = None) -> Dict[str, Any]:
        """进行一局自我对弈"""
        board = Board()
        game_data = []
        
        self.experience_buffer.start_game()
        
        move_count = 0
        start_time = time.time()
        
        while not board.is_game_over():
            current_player = board.current_player
            
            # 选择代理
            if current_player == Player.BLACK:
                agent = self.agent_black
            else:
                agent = self.agent_white
            
            # 调整温度（游戏后期降低随机性）
            current_temp = self.temperature if move_count < self.temperature_threshold else 0.1
            agent.temperature = current_temp
            
            # 获取当前状态
            board_state = board.get_state_for_ai(current_player)
            
            # 获取MCTS策略概率
            action_probs = agent.get_action_probs(board, current_temp)
            
            # 选择移动
            move = agent.get_move(board)
            x, y = move
            
            # 记录经验
            self.experience_buffer.add_experience(
                board_state, action_probs, current_player.value, move
            )
            
            # 记录游戏数据
            game_data.append({
                'move_number': move_count + 1,
                'player': current_player.value,
                'move': move,
                'board_state': board_state.copy(),
                'action_probs': action_probs.copy(),
                'temperature': current_temp
            })
            
            # 执行移动
            board.make_move(x, y, current_player)
            move_count += 1
        
        # 游戏结束，确定获胜者
        winner = board.check_winner()
        if winner is None:
            winner_value = 0  # 平局
        else:
            winner_value = winner.value
        
        # 完成游戏，设置经验价值
        self.experience_buffer.finish_game(winner_value)
        
        game_duration = time.time() - start_time
        
        # 返回游戏结果
        result = {
            'game_id': game_id,
            'winner': winner_value,
            'winner_name': winner.name if winner else 'Draw',
            'move_count': move_count,
            'game_duration': game_duration,
            'game_data': game_data,
            'final_board': board.get_board_state().tolist()
        }
        
        return result
    
    def play_games(self, num_games: int, callback=None) -> List[Dict[str, Any]]:
        """进行多局自我对弈"""
        results = []
        
        for i in range(num_games):
            game_result = self.play_game(i + 1)
            results.append(game_result)
            
            # 调用回调函数（用于进度更新）
            if callback:
                callback(i + 1, num_games, game_result)
        
        return results
    
    def get_statistics(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算自我对弈统计信息"""
        if not results:
            return {}
        
        total_games = len(results)
        black_wins = sum(1 for r in results if r['winner'] == Player.BLACK.value)
        white_wins = sum(1 for r in results if r['winner'] == Player.WHITE.value)
        draws = sum(1 for r in results if r['winner'] == 0)
        
        avg_moves = np.mean([r['move_count'] for r in results])
        avg_duration = np.mean([r['game_duration'] for r in results])
        
        return {
            'total_games': total_games,
            'black_wins': black_wins,
            'white_wins': white_wins,
            'draws': draws,
            'black_win_rate': black_wins / total_games,
            'white_win_rate': white_wins / total_games,
            'draw_rate': draws / total_games,
            'avg_moves_per_game': avg_moves,
            'avg_game_duration': avg_duration,
            'total_duration': sum(r['game_duration'] for r in results)
        }

class SelfPlayEvaluator:
    """自我对弈评估器"""
    
    def __init__(self, model_manager: ModelManager):
        self.model_manager = model_manager
    
    def evaluate_model(self, model_path: str, num_games: int = 100, 
                      opponent_model_path: str = None) -> Dict[str, Any]:
        """评估模型性能"""
        # 加载要评估的模型
        if not self.model_manager.load_model(model_path):
            raise ValueError(f"Failed to load model: {model_path}")
        
        # 创建代理
        agent1 = MCTSAgent(Player.BLACK, self.model_manager, simulations=400)
        
        if opponent_model_path:
            # 加载对手模型
            opponent_manager = ModelManager()
            if not opponent_manager.load_model(opponent_model_path):
                raise ValueError(f"Failed to load opponent model: {opponent_model_path}")
            agent2 = MCTSAgent(Player.WHITE, opponent_manager, simulations=400)
        else:
            # 使用随机代理作为对手
            from ai.agents.random_agent import RandomAgent
            agent2 = RandomAgent(Player.WHITE)
        
        results = []
        
        for game_id in range(num_games):
            result = self._play_evaluation_game(agent1, agent2, game_id)
            results.append(result)
        
        return self._calculate_evaluation_stats(results)
    
    def _play_evaluation_game(self, agent1, agent2, game_id: int) -> Dict[str, Any]:
        """进行一局评估游戏"""
        board = Board()
        moves = []
        start_time = time.time()
        
        while not board.is_game_over():
            current_player = board.current_player
            
            # 选择代理
            if current_player == Player.BLACK:
                agent = agent1
            else:
                agent = agent2
            
            # 获取移动
            move = agent.get_move(board)
            x, y = move
            
            # 记录移动
            moves.append({
                'player': current_player.value,
                'move': move,
                'move_number': len(moves) + 1
            })
            
            # 执行移动
            board.make_move(x, y, current_player)
        
        # 确定获胜者
        winner = board.check_winner()
        game_duration = time.time() - start_time
        
        return {
            'game_id': game_id,
            'winner': winner.value if winner else 0,
            'moves': moves,
            'move_count': len(moves),
            'game_duration': game_duration
        }
    
    def _calculate_evaluation_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算评估统计信息"""
        total_games = len(results)
        agent1_wins = sum(1 for r in results if r['winner'] == Player.BLACK.value)
        agent2_wins = sum(1 for r in results if r['winner'] == Player.WHITE.value)
        draws = sum(1 for r in results if r['winner'] == 0)
        
        return {
            'total_games': total_games,
            'agent1_wins': agent1_wins,
            'agent2_wins': agent2_wins,
            'draws': draws,
            'agent1_win_rate': agent1_wins / total_games,
            'agent2_win_rate': agent2_wins / total_games,
            'draw_rate': draws / total_games,
            'avg_moves_per_game': np.mean([r['move_count'] for r in results]),
            'avg_game_duration': np.mean([r['game_duration'] for r in results])
        }
