"""
AI模型管理器
"""
import torch
import torch.nn as nn
import os
import hashlib
from typing import Optional, Dict, Any, Tuple
from pathlib import Path
import numpy as np

from ai.model.network import GomokuNet, CNNGomoku, ResNetGomoku, TransformerGomoku
from app.models.model import AIModel
from app import db
from datetime import datetime

class ModelManager:
    """AI模型管理器"""
    
    def __init__(self):
        self.current_model: Optional[GomokuNet] = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_cache = {}  # 模型缓存
        
    def create_model(self, architecture: str, **kwargs) -> GomokuNet:
        """
        创建模型
        
        Args:
            architecture: 模型架构 ('cnn', 'resnet', 'transformer')
            **kwargs: 模型参数
            
        Returns:
            GomokuNet: 创建的模型
        """
        board_size = kwargs.get('board_size', 15)
        input_channels = kwargs.get('input_channels', 3)
        
        if architecture == 'cnn':
            hidden_size = kwargs.get('hidden_size', 256)
            model = CNNGomoku(board_size, input_channels, hidden_size)
        elif architecture == 'resnet':
            num_blocks = kwargs.get('num_blocks', 10)
            channels = kwargs.get('channels', 256)
            model = ResNetGomoku(board_size, input_channels, num_blocks, channels)
        elif architecture == 'transformer':
            d_model = kwargs.get('d_model', 256)
            num_layers = kwargs.get('num_layers', 6)
            num_heads = kwargs.get('num_heads', 8)
            model = TransformerGomoku(board_size, input_channels, d_model, num_layers, num_heads)
        else:
            raise ValueError(f"Unknown architecture: {architecture}")
        
        return model.to(self.device)
    
    def save_model(self, model: GomokuNet, model_name: str, version: str, 
                   description: str = "", training_session_id: Optional[int] = None) -> AIModel:
        """
        保存模型
        
        Args:
            model: 要保存的模型
            model_name: 模型名称
            version: 模型版本
            description: 模型描述
            training_session_id: 训练会话ID
            
        Returns:
            AIModel: 保存的模型记录
        """
        # 创建模型目录
        model_dir = Path('data/models')
        model_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        filename = f"{model_name}_v{version}.pth"
        file_path = model_dir / filename
        
        # 保存模型状态
        model_state = {
            'model_state_dict': model.state_dict(),
            'model_config': {
                'architecture': model.__class__.__name__,
                'board_size': model.board_size,
                'input_channels': model.input_channels,
            },
            'version': version,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        torch.save(model_state, file_path)
        
        # 计算文件信息
        file_size = os.path.getsize(file_path)
        checksum = self._calculate_checksum(file_path)
        
        # 计算模型参数数量
        total_params = sum(p.numel() for p in model.parameters())
        
        # 创建数据库记录
        ai_model = AIModel(
            name=model_name,
            version=version,
            description=description,
            status='ready',
            file_path=str(file_path),
            file_size=file_size,
            checksum=checksum,
            architecture=model.__class__.__name__,
            input_size=f"{model.board_size}x{model.board_size}",
            total_parameters=total_params,
            training_session_id=training_session_id,
            created_at=datetime.utcnow()
        )
        
        db.session.add(ai_model)
        db.session.commit()
        
        return ai_model
    
    def load_model(self, model_path: str) -> bool:
        """
        加载模型
        
        Args:
            model_path: 模型文件路径
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if not os.path.exists(model_path):
                return False
            
            # 加载模型状态
            checkpoint = torch.load(model_path, map_location=self.device)
            model_config = checkpoint['model_config']
            
            # 创建模型
            architecture = model_config['architecture']
            if architecture == 'CNNGomoku':
                model = CNNGomoku(
                    model_config['board_size'],
                    model_config['input_channels']
                )
            elif architecture == 'ResNetGomoku':
                model = ResNetGomoku(
                    model_config['board_size'],
                    model_config['input_channels']
                )
            elif architecture == 'TransformerGomoku':
                model = TransformerGomoku(
                    model_config['board_size'],
                    model_config['input_channels']
                )
            else:
                return False
            
            # 加载权重
            model.load_state_dict(checkpoint['model_state_dict'])
            model.to(self.device)
            model.eval()
            
            self.current_model = model
            return True
            
        except Exception as e:
            print(f"Error loading model: {e}")
            return False
    
    def predict(self, board_state: np.ndarray) -> Tuple[np.ndarray, float]:
        """
        使用当前模型进行预测
        
        Args:
            board_state: 棋盘状态 (3, board_size, board_size)
            
        Returns:
            Tuple[np.ndarray, float]: (策略概率, 价值评估)
        """
        if self.current_model is None:
            raise ValueError("No model loaded")
        
        # 转换为张量
        x = torch.FloatTensor(board_state).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            policy_logits, value = self.current_model(x)
            
            # 应用softmax到策略
            policy_probs = torch.softmax(policy_logits, dim=1)
            
            return policy_probs.cpu().numpy()[0], value.cpu().item()
    
    def get_best_move(self, board_state: np.ndarray, valid_moves: list, 
                      temperature: float = 1.0) -> Tuple[int, int]:
        """
        获取最佳移动
        
        Args:
            board_state: 棋盘状态
            valid_moves: 有效移动列表
            temperature: 温度参数，控制随机性
            
        Returns:
            Tuple[int, int]: 最佳移动坐标
        """
        policy_probs, _ = self.predict(board_state)
        
        # 重塑为棋盘形状
        board_size = int(np.sqrt(len(policy_probs)))
        policy_board = policy_probs.reshape(board_size, board_size)
        
        # 只考虑有效移动
        valid_probs = []
        for x, y in valid_moves:
            valid_probs.append(policy_board[x, y])
        
        valid_probs = np.array(valid_probs)
        
        # 应用温度
        if temperature > 0:
            valid_probs = valid_probs ** (1.0 / temperature)
            valid_probs = valid_probs / np.sum(valid_probs)
            
            # 根据概率选择
            move_idx = np.random.choice(len(valid_moves), p=valid_probs)
        else:
            # 选择概率最高的移动
            move_idx = np.argmax(valid_probs)
        
        return valid_moves[move_idx]
    
    def evaluate_position(self, board_state: np.ndarray) -> float:
        """
        评估当前局面
        
        Args:
            board_state: 棋盘状态
            
        Returns:
            float: 局面评估值 (-1到1)
        """
        _, value = self.predict(board_state)
        return value
    
    def _calculate_checksum(self, file_path: str) -> str:
        """计算文件校验和"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def get_model_info(self) -> Optional[Dict[str, Any]]:
        """获取当前模型信息"""
        if self.current_model is None:
            return None
        
        total_params = sum(p.numel() for p in self.current_model.parameters())
        trainable_params = sum(p.numel() for p in self.current_model.parameters() if p.requires_grad)
        
        return {
            'architecture': self.current_model.__class__.__name__,
            'board_size': self.current_model.board_size,
            'input_channels': self.current_model.input_channels,
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'device': str(self.device)
        }
    
    def start_evaluation(self, model_id: int, num_games: int, 
                        opponent_model_id: Optional[int] = None) -> str:
        """
        开始模型评估
        
        Args:
            model_id: 要评估的模型ID
            num_games: 评估游戏数量
            opponent_model_id: 对手模型ID（可选）
            
        Returns:
            str: 评估任务ID
        """
        # 这里可以实现异步评估逻辑
        # 返回一个任务ID用于跟踪评估进度
        import uuid
        evaluation_id = str(uuid.uuid4())
        
        # 启动评估任务（这里简化处理）
        print(f"Started evaluation {evaluation_id} for model {model_id}")
        
        return evaluation_id
