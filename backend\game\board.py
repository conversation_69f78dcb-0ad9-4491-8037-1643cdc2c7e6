"""
五子棋棋盘逻辑
"""
import numpy as np
from typing import List, Tuple, Optional
from enum import Enum

class Player(Enum):
    """玩家枚举"""
    EMPTY = 0
    BLACK = 1
    WHITE = 2

class Board:
    """五子棋棋盘类"""
    
    def __init__(self, size: int = 15):
        """
        初始化棋盘
        
        Args:
            size: 棋盘大小，默认15x15
        """
        self.size = size
        self.board = np.zeros((size, size), dtype=int)
        self.move_history = []  # 移动历史
        self.current_player = Player.BLACK
        
    def is_valid_position(self, x: int, y: int) -> bool:
        """
        检查位置是否有效
        
        Args:
            x, y: 坐标位置
            
        Returns:
            bool: 位置是否有效
        """
        return 0 <= x < self.size and 0 <= y < self.size
    
    def is_empty(self, x: int, y: int) -> bool:
        """
        检查位置是否为空
        
        Args:
            x, y: 坐标位置
            
        Returns:
            bool: 位置是否为空
        """
        if not self.is_valid_position(x, y):
            return False
        return self.board[x][y] == Player.EMPTY.value
    
    def make_move(self, x: int, y: int, player: Player) -> bool:
        """
        执行移动
        
        Args:
            x, y: 坐标位置
            player: 玩家
            
        Returns:
            bool: 移动是否成功
        """
        if not self.is_valid_position(x, y) or not self.is_empty(x, y):
            return False
        
        self.board[x][y] = player.value
        self.move_history.append((x, y, player))
        
        # 切换当前玩家
        self.current_player = Player.WHITE if player == Player.BLACK else Player.BLACK
        
        return True
    
    def undo_move(self) -> bool:
        """
        撤销上一步移动
        
        Returns:
            bool: 撤销是否成功
        """
        if not self.move_history:
            return False
        
        x, y, player = self.move_history.pop()
        self.board[x][y] = Player.EMPTY.value
        
        # 恢复当前玩家
        self.current_player = player
        
        return True
    
    def get_valid_moves(self) -> List[Tuple[int, int]]:
        """
        获取所有有效移动位置
        
        Returns:
            List[Tuple[int, int]]: 有效移动位置列表
        """
        moves = []
        for x in range(self.size):
            for y in range(self.size):
                if self.is_empty(x, y):
                    moves.append((x, y))
        return moves
    
    def get_nearby_moves(self, radius: int = 2) -> List[Tuple[int, int]]:
        """
        获取已有棋子附近的空位置（用于优化AI搜索）
        
        Args:
            radius: 搜索半径
            
        Returns:
            List[Tuple[int, int]]: 附近的空位置列表
        """
        if not self.move_history:
            # 如果没有棋子，返回中心位置
            center = self.size // 2
            return [(center, center)]
        
        nearby_moves = set()
        
        # 遍历所有已有棋子
        for x in range(self.size):
            for y in range(self.size):
                if self.board[x][y] != Player.EMPTY.value:
                    # 搜索周围的空位置
                    for dx in range(-radius, radius + 1):
                        for dy in range(-radius, radius + 1):
                            nx, ny = x + dx, y + dy
                            if (self.is_valid_position(nx, ny) and 
                                self.is_empty(nx, ny)):
                                nearby_moves.add((nx, ny))
        
        return list(nearby_moves)
    
    def check_winner(self) -> Optional[Player]:
        """
        检查是否有获胜者
        
        Returns:
            Optional[Player]: 获胜者，如果没有则返回None
        """
        # 检查所有方向：水平、垂直、对角线
        directions = [
            (0, 1),   # 水平
            (1, 0),   # 垂直
            (1, 1),   # 主对角线
            (1, -1)   # 反对角线
        ]
        
        for x in range(self.size):
            for y in range(self.size):
                if self.board[x][y] == Player.EMPTY.value:
                    continue
                
                current_player = Player(self.board[x][y])
                
                # 检查每个方向
                for dx, dy in directions:
                    count = 1  # 当前位置算一个
                    
                    # 向前检查
                    nx, ny = x + dx, y + dy
                    while (self.is_valid_position(nx, ny) and 
                           self.board[nx][ny] == current_player.value):
                        count += 1
                        nx += dx
                        ny += dy
                    
                    # 向后检查
                    nx, ny = x - dx, y - dy
                    while (self.is_valid_position(nx, ny) and 
                           self.board[nx][ny] == current_player.value):
                        count += 1
                        nx -= dx
                        ny -= dy
                    
                    # 如果连成5个或以上，获胜
                    if count >= 5:
                        return current_player
        
        return None
    
    def is_game_over(self) -> bool:
        """
        检查游戏是否结束
        
        Returns:
            bool: 游戏是否结束
        """
        # 有获胜者或棋盘满了
        return self.check_winner() is not None or len(self.get_valid_moves()) == 0
    
    def get_board_state(self) -> np.ndarray:
        """
        获取棋盘状态
        
        Returns:
            np.ndarray: 棋盘状态数组
        """
        return self.board.copy()
    
    def get_state_for_ai(self, player: Player) -> np.ndarray:
        """
        获取用于AI的棋盘状态表示
        
        Args:
            player: 当前玩家
            
        Returns:
            np.ndarray: AI用的状态表示 (3, size, size)
        """
        # 创建3个通道：当前玩家、对手、空位
        state = np.zeros((3, self.size, self.size))
        
        for x in range(self.size):
            for y in range(self.size):
                if self.board[x][y] == player.value:
                    state[0][x][y] = 1  # 当前玩家
                elif self.board[x][y] != Player.EMPTY.value:
                    state[1][x][y] = 1  # 对手
                else:
                    state[2][x][y] = 1  # 空位
        
        return state
    
    def copy(self) -> 'Board':
        """
        创建棋盘副本
        
        Returns:
            Board: 棋盘副本
        """
        new_board = Board(self.size)
        new_board.board = self.board.copy()
        new_board.move_history = self.move_history.copy()
        new_board.current_player = self.current_player
        return new_board
    
    def reset(self):
        """重置棋盘"""
        self.board = np.zeros((self.size, self.size), dtype=int)
        self.move_history = []
        self.current_player = Player.BLACK
    
    def __str__(self) -> str:
        """字符串表示"""
        symbols = {Player.EMPTY.value: '.', Player.BLACK.value: 'X', Player.WHITE.value: 'O'}
        result = []
        
        # 添加列号
        result.append('  ' + ' '.join(f'{i:2d}' for i in range(self.size)))
        
        for i, row in enumerate(self.board):
            row_str = f'{i:2d} ' + ' '.join(f' {symbols[cell]}' for cell in row)
            result.append(row_str)
        
        return '\n'.join(result)
