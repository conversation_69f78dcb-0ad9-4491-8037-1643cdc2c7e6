"""
游戏相关WebSocket事件处理
"""
from flask_socketio import emit, join_room, leave_room, disconnect
from app import socketio, db
from app.models.game import Game, GameMove
from game.engine import GameEngine
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@socketio.on('connect')
def on_connect():
    """客户端连接事件"""
    logger.info(f'Client connected: {request.sid}')
    emit('connected', {'status': 'success', 'message': 'Connected to game server'})

@socketio.on('disconnect')
def on_disconnect():
    """客户端断开连接事件"""
    logger.info(f'Client disconnected: {request.sid}')

@socketio.on('join_game')
def on_join_game(data):
    """加入游戏房间"""
    try:
        game_id = data.get('game_id')
        if not game_id:
            emit('error', {'message': 'Game ID is required'})
            return
        
        game = Game.query.get(game_id)
        if not game:
            emit('error', {'message': 'Game not found'})
            return
        
        room = f'game_{game_id}'
        join_room(room)
        
        # 发送当前游戏状态
        engine = GameEngine(game_id)
        board_state = engine.get_board_state()
        
        emit('game_joined', {
            'game_id': game_id,
            'board_state': board_state,
            'current_player': game.current_player,
            'status': game.status
        })
        
        # 通知房间内其他玩家
        emit('player_joined', {
            'message': 'A player joined the game'
        }, room=room, include_self=False)
        
        logger.info(f'Client {request.sid} joined game {game_id}')
        
    except Exception as e:
        logger.error(f'Error joining game: {str(e)}')
        emit('error', {'message': 'Failed to join game'})

@socketio.on('leave_game')
def on_leave_game(data):
    """离开游戏房间"""
    try:
        game_id = data.get('game_id')
        if game_id:
            room = f'game_{game_id}'
            leave_room(room)
            
            # 通知房间内其他玩家
            emit('player_left', {
                'message': 'A player left the game'
            }, room=room)
            
            logger.info(f'Client {request.sid} left game {game_id}')
        
    except Exception as e:
        logger.error(f'Error leaving game: {str(e)}')

@socketio.on('make_move')
def on_make_move(data):
    """处理游戏移动"""
    try:
        game_id = data.get('game_id')
        x = data.get('x')
        y = data.get('y')
        
        if not all([game_id, x is not None, y is not None]):
            emit('error', {'message': 'Missing required parameters'})
            return
        
        game = Game.query.get(game_id)
        if not game:
            emit('error', {'message': 'Game not found'})
            return
        
        if game.status != 'active':
            emit('error', {'message': 'Game is not active'})
            return
        
        # 执行移动
        engine = GameEngine(game_id)
        result = engine.make_move(x, y, game.current_player)
        
        if result['success']:
            room = f'game_{game_id}'
            
            # 广播移动结果给房间内所有玩家
            emit('move_made', {
                'game_id': game_id,
                'x': x,
                'y': y,
                'player': game.current_player,
                'game_status': result['game_status'],
                'winner': result.get('winner'),
                'next_player': result.get('next_player')
            }, room=room)
            
            # 如果游戏结束，发送游戏结束事件
            if result['game_status'] == 'finished':
                emit('game_finished', {
                    'game_id': game_id,
                    'winner': result['winner'],
                    'reason': result.get('reason', 'normal')
                }, room=room)
            
            # 如果是AI回合，发送AI移动
            if result.get('ai_move'):
                ai_move = result['ai_move']
                emit('ai_move', {
                    'game_id': game_id,
                    'x': ai_move['x'],
                    'y': ai_move['y'],
                    'player': ai_move['player'],
                    'thinking_time': ai_move.get('thinking_time', 0)
                }, room=room)
        else:
            emit('error', {'message': result['error']})
            
    except Exception as e:
        logger.error(f'Error making move: {str(e)}')
        emit('error', {'message': 'Failed to make move'})

@socketio.on('request_hint')
def on_request_hint(data):
    """请求游戏提示"""
    try:
        game_id = data.get('game_id')
        if not game_id:
            emit('error', {'message': 'Game ID is required'})
            return
        
        game = Game.query.get(game_id)
        if not game:
            emit('error', {'message': 'Game not found'})
            return
        
        # 获取AI建议的移动
        engine = GameEngine(game_id)
        hint = engine.get_hint(game.current_player)
        
        if hint:
            emit('hint_received', {
                'game_id': game_id,
                'x': hint['x'],
                'y': hint['y'],
                'confidence': hint.get('confidence', 0),
                'reasoning': hint.get('reasoning', '')
            })
        else:
            emit('error', {'message': 'Unable to generate hint'})
            
    except Exception as e:
        logger.error(f'Error requesting hint: {str(e)}')
        emit('error', {'message': 'Failed to get hint'})

@socketio.on('surrender')
def on_surrender(data):
    """投降"""
    try:
        game_id = data.get('game_id')
        if not game_id:
            emit('error', {'message': 'Game ID is required'})
            return
        
        game = Game.query.get(game_id)
        if not game:
            emit('error', {'message': 'Game not found'})
            return
        
        if game.status != 'active':
            emit('error', {'message': 'Game is not active'})
            return
        
        # 设置游戏结束状态
        game.status = 'finished'
        game.winner = 'white' if game.current_player == 'black' else 'black'
        game.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        room = f'game_{game_id}'
        emit('game_finished', {
            'game_id': game_id,
            'winner': game.winner,
            'reason': 'surrender'
        }, room=room)
        
        logger.info(f'Game {game_id} ended by surrender')
        
    except Exception as e:
        logger.error(f'Error handling surrender: {str(e)}')
        emit('error', {'message': 'Failed to surrender'})
