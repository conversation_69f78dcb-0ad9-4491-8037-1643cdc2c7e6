"""
AI代理基类
"""
from abc import ABC, abstractmethod
from typing import <PERSON>ple, Optional
import numpy as np
from game.board import Board, Player

class BaseAgent(ABC):
    """AI代理基类"""
    
    def __init__(self, player: Player, name: str = "BaseAgent"):
        """
        初始化代理
        
        Args:
            player: 代理控制的玩家
            name: 代理名称
        """
        self.player = player
        self.name = name
        self.move_count = 0
        self.total_thinking_time = 0.0
    
    @abstractmethod
    def get_move(self, board: Board) -> Tuple[int, int]:
        """
        获取下一步移动
        
        Args:
            board: 当前棋盘状态
            
        Returns:
            Tuple[int, int]: 移动坐标 (x, y)
        """
        pass
    
    def reset(self):
        """重置代理状态"""
        self.move_count = 0
        self.total_thinking_time = 0.0
    
    def get_stats(self) -> dict:
        """获取代理统计信息"""
        avg_thinking_time = (self.total_thinking_time / self.move_count 
                           if self.move_count > 0 else 0)
        
        return {
            'name': self.name,
            'player': self.player.name,
            'move_count': self.move_count,
            'total_thinking_time': self.total_thinking_time,
            'avg_thinking_time': avg_thinking_time
        }
    
    def __str__(self) -> str:
        return f"{self.name} ({self.player.name})"

class RandomAgent(BaseAgent):
    """随机代理"""
    
    def __init__(self, player: Player):
        super().__init__(player, "RandomAgent")
    
    def get_move(self, board: Board) -> Tuple[int, int]:
        """随机选择一个有效移动"""
        import random
        import time
        
        start_time = time.time()
        
        valid_moves = board.get_valid_moves()
        if not valid_moves:
            raise ValueError("No valid moves available")
        
        move = random.choice(valid_moves)
        
        # 更新统计
        thinking_time = time.time() - start_time
        self.move_count += 1
        self.total_thinking_time += thinking_time
        
        return move

class HumanAgent(BaseAgent):
    """人类代理（用于测试）"""
    
    def __init__(self, player: Player):
        super().__init__(player, "HumanAgent")
    
    def get_move(self, board: Board) -> Tuple[int, int]:
        """获取人类输入的移动"""
        print(f"\n{board}")
        print(f"Player {self.player.name}'s turn")
        
        while True:
            try:
                x = int(input("Enter x coordinate (0-14): "))
                y = int(input("Enter y coordinate (0-14): "))
                
                if board.is_valid_position(x, y) and board.is_empty(x, y):
                    self.move_count += 1
                    return (x, y)
                else:
                    print("Invalid move! Please try again.")
                    
            except ValueError:
                print("Please enter valid numbers!")
            except KeyboardInterrupt:
                print("\nGame interrupted by user")
                raise
