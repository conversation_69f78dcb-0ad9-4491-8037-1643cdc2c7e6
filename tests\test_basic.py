"""
基础功能测试
"""
import pytest
import sys
from pathlib import Path

# 添加backend目录到Python路径
backend_path = Path(__file__).parent.parent / 'backend'
sys.path.insert(0, str(backend_path))

from game.board import Board, Player
from game.rules import GameRules
from ai.model.manager import ModelManager

class TestBoard:
    """测试棋盘功能"""
    
    def test_board_initialization(self):
        """测试棋盘初始化"""
        board = Board()
        assert board.size == 15
        assert board.current_player == Player.BLACK
        assert len(board.move_history) == 0
    
    def test_valid_move(self):
        """测试有效移动"""
        board = Board()
        result = board.make_move(7, 7, Player.BLACK)
        assert result == True
        assert board.board[7][7] == Player.BLACK.value
        assert len(board.move_history) == 1
    
    def test_invalid_move(self):
        """测试无效移动"""
        board = Board()
        # 先放一个棋子
        board.make_move(7, 7, Player.BLACK)
        # 尝试在同一位置再放棋子
        result = board.make_move(7, 7, <PERSON>.WHITE)
        assert result == False
    
    def test_out_of_bounds(self):
        """测试越界移动"""
        board = Board()
        result = board.make_move(15, 15, Player.BLACK)
        assert result == False
        result = board.make_move(-1, -1, Player.BLACK)
        assert result == False
    
    def test_undo_move(self):
        """测试撤销移动"""
        board = Board()
        board.make_move(7, 7, Player.BLACK)
        assert board.board[7][7] == Player.BLACK.value
        
        result = board.undo_move()
        assert result == True
        assert board.board[7][7] == Player.EMPTY.value
        assert len(board.move_history) == 0

class TestGameRules:
    """测试游戏规则"""
    
    def test_rules_initialization(self):
        """测试规则初始化"""
        rules = GameRules()
        assert rules.board_size == 15
        assert rules.win_length == 5
    
    def test_horizontal_win(self):
        """测试水平获胜"""
        board = Board()
        rules = GameRules()
        
        # 放置5个连续的黑棋
        for i in range(5):
            board.make_move(7, 5 + i, Player.BLACK)
        
        # 检查是否获胜
        winner = board.check_winner()
        assert winner == Player.BLACK
    
    def test_vertical_win(self):
        """测试垂直获胜"""
        board = Board()
        rules = GameRules()
        
        # 放置5个连续的白棋
        for i in range(5):
            board.make_move(5 + i, 7, Player.WHITE)
        
        winner = board.check_winner()
        assert winner == Player.WHITE
    
    def test_diagonal_win(self):
        """测试对角线获胜"""
        board = Board()
        rules = GameRules()
        
        # 放置5个对角线的黑棋
        for i in range(5):
            board.make_move(5 + i, 5 + i, Player.BLACK)
        
        winner = board.check_winner()
        assert winner == Player.BLACK
    
    def test_no_winner(self):
        """测试无获胜者"""
        board = Board()
        
        # 放置一些不连续的棋子
        board.make_move(7, 7, Player.BLACK)
        board.make_move(8, 8, Player.WHITE)
        board.make_move(9, 9, Player.BLACK)
        
        winner = board.check_winner()
        assert winner is None

class TestModelManager:
    """测试模型管理器"""
    
    def test_model_manager_initialization(self):
        """测试模型管理器初始化"""
        manager = ModelManager()
        assert manager.current_model is None
        assert manager.device is not None
    
    def test_create_cnn_model(self):
        """测试创建CNN模型"""
        manager = ModelManager()
        model = manager.create_model('cnn', board_size=15, input_channels=3, hidden_size=128)
        assert model is not None
        assert hasattr(model, 'forward')
    
    def test_create_resnet_model(self):
        """测试创建ResNet模型"""
        manager = ModelManager()
        model = manager.create_model('resnet', board_size=15, input_channels=3, num_blocks=5)
        assert model is not None
        assert hasattr(model, 'forward')
    
    def test_invalid_architecture(self):
        """测试无效的模型架构"""
        manager = ModelManager()
        with pytest.raises(ValueError):
            manager.create_model('invalid_arch')

class TestGameIntegration:
    """测试游戏集成功能"""
    
    def test_complete_game_flow(self):
        """测试完整的游戏流程"""
        board = Board()
        rules = GameRules()
        
        # 模拟一个简单的游戏
        moves = [
            (7, 7, Player.BLACK),
            (7, 8, Player.WHITE),
            (8, 7, Player.BLACK),
            (8, 8, Player.WHITE),
            (9, 7, Player.BLACK),
            (9, 8, Player.WHITE),
            (10, 7, Player.BLACK),
            (10, 8, Player.WHITE),
            (11, 7, Player.BLACK)  # 黑棋获胜
        ]
        
        winner = None
        for x, y, player in moves:
            board.make_move(x, y, player)
            winner = board.check_winner()
            if winner:
                break
        
        assert winner == Player.BLACK
        assert len(board.move_history) == 9
    
    def test_board_state_representation(self):
        """测试棋盘状态表示"""
        board = Board()
        
        # 放置一些棋子
        board.make_move(7, 7, Player.BLACK)
        board.make_move(7, 8, Player.WHITE)
        
        # 获取AI用的状态表示
        state = board.get_state_for_ai(Player.BLACK)
        assert state.shape == (3, 15, 15)
        
        # 检查状态表示的正确性
        assert state[0][7][7] == 1  # 黑棋位置
        assert state[1][7][8] == 1  # 白棋位置
        assert state[2][0][0] == 1  # 空位

if __name__ == '__main__':
    pytest.main([__file__, '-v'])
