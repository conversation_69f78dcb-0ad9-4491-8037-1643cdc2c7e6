/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #2c3e50;
    font-size: 2.5em;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-controls {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.btn-secondary {
    background: linear-gradient(45deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-success {
    background: linear-gradient(45deg, #27ae60, #229954);
    color: white;
}

.btn-warning {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
    color: white;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

/* 游戏设置面板 */
.game-setup {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    min-width: 300px;
}

.game-setup h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #34495e;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
}

/* 游戏区域 */
.game-area {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* 游戏信息 */
.game-info {
    margin-bottom: 20px;
}

.player-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.player {
    display: flex;
    align-items: center;
    gap: 10px;
}

.player-stone {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 2px solid #333;
}

.black-stone {
    background: radial-gradient(circle at 30% 30%, #666, #000);
}

.white-stone {
    background: radial-gradient(circle at 30% 30%, #fff, #ddd);
}

.player-name {
    font-weight: 600;
    font-size: 16px;
}

.game-status {
    text-align: center;
}

.current-turn {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.move-count, .game-time {
    font-size: 14px;
    color: #7f8c8d;
}

/* 棋盘容器 */
.board-container {
    position: relative;
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.game-board {
    background: #deb887;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.board-line {
    stroke: #8b4513;
    stroke-width: 1;
}

.board-intersection {
    fill: transparent;
    cursor: pointer;
}

.board-intersection:hover {
    fill: rgba(52, 152, 219, 0.3);
}

.board-stone {
    cursor: pointer;
}

.black-game-stone {
    fill: url(#blackGradient);
    stroke: #333;
    stroke-width: 1;
}

.white-game-stone {
    fill: url(#whiteGradient);
    stroke: #333;
    stroke-width: 1;
}

.last-move {
    stroke: #e74c3c;
    stroke-width: 3;
}

.winning-stone {
    stroke: #f39c12;
    stroke-width: 4;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { stroke-width: 4; }
    50% { stroke-width: 6; }
    100% { stroke-width: 4; }
}

.hint-indicator {
    fill: rgba(46, 204, 113, 0.6);
    stroke: #27ae60;
    stroke-width: 2;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* 移动历史 */
.move-history {
    margin-top: 20px;
    max-height: 200px;
    overflow-y: auto;
}

.move-history h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.history-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.history-item {
    padding: 5px 10px;
    background: #ecf0f1;
    border-radius: 5px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.history-item:hover {
    background: #bdc3c7;
}

.history-item.black {
    background: #34495e;
    color: white;
}

.history-item.white {
    background: #ecf0f1;
    color: #2c3e50;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    text-align: center;
    min-width: 300px;
}

.modal-content h2, .modal-content h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.modal-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* 加载指示器 */
.loading {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999;
    backdrop-filter: blur(5px);
}

.spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    border: 5px solid #ecf0f1;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.loading p {
    position: absolute;
    top: 60%;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 18px;
    font-weight: 600;
}

/* 连接状态 */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 15px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #e74c3c;
    animation: pulse-status 2s infinite;
}

.status-indicator.connected {
    background: #27ae60;
    animation: none;
}

@keyframes pulse-status {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .game-setup {
        min-width: auto;
    }
    
    .player-info {
        flex-direction: column;
        gap: 15px;
    }
    
    .game-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
}
