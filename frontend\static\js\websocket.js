/**
 * WebSocket通信管理器
 */

class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.eventHandlers = new Map();
        
        this.init();
    }
    
    init() {
        try {
            // 初始化Socket.IO连接
            this.socket = io(CONFIG.WEBSOCKET.NAMESPACE, {
                transports: ['websocket', 'polling'],
                timeout: 5000,
                forceNew: true
            });
            
            this.setupEventHandlers();
            this.setupConnectionHandlers();
            
        } catch (error) {
            console.error('WebSocket initialization failed:', error);
            this.updateConnectionStatus(false, 'Connection failed');
        }
    }
    
    setupConnectionHandlers() {
        // 连接成功
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.CONNECT, () => {
            console.log('WebSocket connected');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateConnectionStatus(true, 'Connected');
            
            // 触发连接成功事件
            this.emit('connected');
        });
        
        // 连接断开
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.DISCONNECT, (reason) => {
            console.log('WebSocket disconnected:', reason);
            this.isConnected = false;
            this.updateConnectionStatus(false, 'Disconnected');
            
            // 触发断开连接事件
            this.emit('disconnected', reason);
            
            // 尝试重连
            if (reason !== 'io client disconnect') {
                this.attemptReconnect();
            }
        });
        
        // 连接错误
        this.socket.on('connect_error', (error) => {
            console.error('WebSocket connection error:', error);
            this.updateConnectionStatus(false, 'Connection error');
            this.attemptReconnect();
        });
        
        // 重连失败
        this.socket.on('reconnect_failed', () => {
            console.error('WebSocket reconnection failed');
            this.updateConnectionStatus(false, 'Reconnection failed');
        });
    }
    
    setupEventHandlers() {
        // 游戏事件
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.GAME_JOINED, (data) => {
            this.emit('gameJoined', data);
        });
        
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.MOVE_MADE, (data) => {
            this.emit('moveMade', data);
        });
        
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.AI_MOVE, (data) => {
            this.emit('aiMove', data);
        });
        
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.GAME_FINISHED, (data) => {
            this.emit('gameFinished', data);
        });
        
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.HINT_RECEIVED, (data) => {
            this.emit('hintReceived', data);
        });
        
        // 训练事件
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.TRAINING_UPDATE, (data) => {
            this.emit('trainingUpdate', data);
        });
        
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.TRAINING_PROGRESS, (data) => {
            this.emit('trainingProgress', data);
        });
        
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.TRAINING_COMPLETED, (data) => {
            this.emit('trainingCompleted', data);
        });
        
        // 错误事件
        this.socket.on(CONFIG.WEBSOCKET.EVENTS.ERROR, (data) => {
            console.error('WebSocket error:', data);
            this.emit('error', data);
        });
    }
    
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.updateConnectionStatus(false, 'Connection failed');
            return;
        }
        
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
        this.updateConnectionStatus(false, `Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.socket.connect();
            }
        }, delay);
    }
    
    updateConnectionStatus(connected, message) {
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        if (statusIndicator && statusText) {
            statusIndicator.className = `status-indicator ${connected ? 'connected' : ''}`;
            statusText.textContent = message;
        }
    }
    
    // 事件监听器管理
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }
    
    off(event, handler) {
        if (this.eventHandlers.has(event)) {
            const handlers = this.eventHandlers.get(event);
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    
    emit(event, data) {
        if (this.eventHandlers.has(event)) {
            this.eventHandlers.get(event).forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in event handler for ${event}:`, error);
                }
            });
        }
    }
    
    // 游戏相关方法
    joinGame(gameId) {
        if (this.isConnected) {
            this.socket.emit(CONFIG.WEBSOCKET.EVENTS.JOIN_GAME, { game_id: gameId });
        } else {
            console.error('Cannot join game: WebSocket not connected');
        }
    }
    
    leaveGame(gameId) {
        if (this.isConnected) {
            this.socket.emit(CONFIG.WEBSOCKET.EVENTS.LEAVE_GAME, { game_id: gameId });
        }
    }
    
    makeMove(gameId, x, y) {
        if (this.isConnected) {
            this.socket.emit(CONFIG.WEBSOCKET.EVENTS.MAKE_MOVE, {
                game_id: gameId,
                x: x,
                y: y
            });
        } else {
            console.error('Cannot make move: WebSocket not connected');
        }
    }
    
    requestHint(gameId) {
        if (this.isConnected) {
            this.socket.emit(CONFIG.WEBSOCKET.EVENTS.REQUEST_HINT, { game_id: gameId });
        } else {
            console.error('Cannot request hint: WebSocket not connected');
        }
    }
    
    surrender(gameId) {
        if (this.isConnected) {
            this.socket.emit(CONFIG.WEBSOCKET.EVENTS.SURRENDER, { game_id: gameId });
        } else {
            console.error('Cannot surrender: WebSocket not connected');
        }
    }
    
    // 训练监控相关方法
    joinTrainingMonitor() {
        if (this.isConnected) {
            this.socket.emit(CONFIG.WEBSOCKET.EVENTS.JOIN_TRAINING_MONITOR);
        }
    }
    
    leaveTrainingMonitor() {
        if (this.isConnected) {
            this.socket.emit('leave_training_monitor');
        }
    }
    
    // 连接状态检查
    isSocketConnected() {
        return this.isConnected && this.socket && this.socket.connected;
    }
    
    // 手动重连
    reconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket.connect();
        }
    }
    
    // 断开连接
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
        }
    }
    
    // 获取连接状态
    getConnectionInfo() {
        return {
            connected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            socketId: this.socket ? this.socket.id : null
        };
    }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager();
