"""
数据库工具函数
"""
from app import db
from app.models import *
from datetime import datetime
import os

def init_database():
    """初始化数据库"""
    # 创建所有表
    db.create_all()
    
    # 创建默认数据
    create_default_data()
    
    print("Database initialized successfully!")

def create_default_data():
    """创建默认数据"""
    # 检查是否已有数据
    if TrainingConfig.query.first():
        return
    
    # 创建默认训练配置
    default_configs = [
        {
            'name': 'Quick Training',
            'description': '快速训练配置，适合测试',
            'epochs': 100,
            'batch_size': 16,
            'learning_rate': 0.01,
            'self_play_games': 50,
            'model_name': 'quick_model',
            'model_architecture': 'cnn',
            'hidden_size': 128,
            'num_layers': 5
        },
        {
            'name': 'Standard Training',
            'description': '标准训练配置，平衡性能和时间',
            'epochs': 1000,
            'batch_size': 32,
            'learning_rate': 0.001,
            'self_play_games': 100,
            'model_name': 'standard_model',
            'model_architecture': 'resnet',
            'hidden_size': 256,
            'num_layers': 10
        },
        {
            'name': 'Advanced Training',
            'description': '高级训练配置，追求最佳性能',
            'epochs': 5000,
            'batch_size': 64,
            'learning_rate': 0.0005,
            'self_play_games': 200,
            'model_name': 'advanced_model',
            'model_architecture': 'resnet',
            'hidden_size': 512,
            'num_layers': 20
        }
    ]
    
    for config_data in default_configs:
        config = TrainingConfig(**config_data)
        db.session.add(config)
    
    # 提交更改
    db.session.commit()
    print("Default training configurations created!")

def reset_database():
    """重置数据库"""
    # 删除所有表
    db.drop_all()
    
    # 重新创建
    init_database()
    
    print("Database reset successfully!")

def backup_database(backup_path: str):
    """备份数据库"""
    if not os.path.exists(os.path.dirname(backup_path)):
        os.makedirs(os.path.dirname(backup_path))
    
    # 这里可以实现具体的备份逻辑
    # 对于SQLite，可以直接复制文件
    # 对于其他数据库，可以使用相应的备份命令
    
    print(f"Database backed up to {backup_path}")

def get_database_stats():
    """获取数据库统计信息"""
    stats = {
        'games': {
            'total': Game.query.count(),
            'active': Game.query.filter_by(status='active').count(),
            'finished': Game.query.filter_by(status='finished').count()
        },
        'training_sessions': {
            'total': TrainingSession.query.count(),
            'running': TrainingSession.query.filter_by(status='running').count(),
            'completed': TrainingSession.query.filter_by(status='completed').count()
        },
        'models': {
            'total': AIModel.query.count(),
            'ready': AIModel.query.filter_by(status='ready').count(),
            'active': AIModel.query.filter_by(is_active=True).count()
        }
    }
    
    return stats

def cleanup_old_data(days: int = 30):
    """清理旧数据"""
    from datetime import timedelta
    
    cutoff_date = datetime.utcnow() - timedelta(days=days)
    
    # 清理旧的已完成游戏
    old_games = Game.query.filter(
        Game.status == 'finished',
        Game.finished_at < cutoff_date
    ).all()
    
    for game in old_games:
        # 删除相关的移动记录
        GameMove.query.filter_by(game_id=game.id).delete()
        db.session.delete(game)
    
    # 清理旧的训练指标（保留会话但删除详细指标）
    old_metrics = TrainingMetric.query.filter(
        TrainingMetric.timestamp < cutoff_date
    ).all()
    
    for metric in old_metrics:
        db.session.delete(metric)
    
    db.session.commit()
    
    print(f"Cleaned up data older than {days} days")

def optimize_database():
    """优化数据库"""
    # 这里可以实现数据库优化逻辑
    # 例如重建索引、分析表等
    
    print("Database optimized!")

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'init':
            init_database()
        elif command == 'reset':
            reset_database()
        elif command == 'stats':
            stats = get_database_stats()
            print("Database Statistics:")
            for table, data in stats.items():
                print(f"  {table}:")
                for key, value in data.items():
                    print(f"    {key}: {value}")
        elif command == 'cleanup':
            days = int(sys.argv[2]) if len(sys.argv) > 2 else 30
            cleanup_old_data(days)
        elif command == 'optimize':
            optimize_database()
        else:
            print("Unknown command. Available commands: init, reset, stats, cleanup, optimize")
    else:
        print("Usage: python database.py <command>")
        print("Commands: init, reset, stats, cleanup [days], optimize")
