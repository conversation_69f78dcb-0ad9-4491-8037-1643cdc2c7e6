"""
游戏相关数据模型
"""
from app import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, Float

class Game(db.Model):
    """游戏表"""
    __tablename__ = 'games'
    
    id = Column(Integer, primary_key=True)
    player_type = Column(String(20), nullable=False)  # 'human', 'ai'
    difficulty = Column(String(20), default='medium')  # 'easy', 'medium', 'hard'
    status = Column(String(20), default='active')  # 'active', 'finished', 'abandoned'
    current_player = Column(String(10), default='black')  # 'black', 'white'
    winner = Column(String(10))  # 'black', 'white', 'draw', None
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.utcnow)
    finished_at = Column(DateTime)
    
    # 游戏统计
    total_moves = Column(Integer, default=0)
    game_duration = Column(Float)  # 游戏时长（秒）
    
    # 关联关系
    moves = db.relationship('GameMove', backref='game', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Game {self.id}: {self.player_type} vs AI, Status: {self.status}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'player_type': self.player_type,
            'difficulty': self.difficulty,
            'status': self.status,
            'current_player': self.current_player,
            'winner': self.winner,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'finished_at': self.finished_at.isoformat() if self.finished_at else None,
            'total_moves': self.total_moves,
            'game_duration': self.game_duration
        }

class GameMove(db.Model):
    """游戏移动表"""
    __tablename__ = 'game_moves'
    
    id = Column(Integer, primary_key=True)
    game_id = Column(Integer, db.ForeignKey('games.id'), nullable=False)
    move_number = Column(Integer, nullable=False)  # 移动序号
    player = Column(String(10), nullable=False)  # 'black', 'white'
    x = Column(Integer, nullable=False)  # X坐标
    y = Column(Integer, nullable=False)  # Y坐标
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # AI相关信息（如果是AI移动）
    thinking_time = Column(Float)  # AI思考时间
    evaluation_score = Column(Float)  # 位置评估分数
    strategy = Column(String(50))  # AI策略类型
    
    # 索引
    __table_args__ = (
        db.Index('idx_game_move', 'game_id', 'move_number'),
    )
    
    def __repr__(self):
        return f'<GameMove {self.id}: Game {self.game_id}, Move {self.move_number}, {self.player} at ({self.x}, {self.y})>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'game_id': self.game_id,
            'move_number': self.move_number,
            'player': self.player,
            'x': self.x,
            'y': self.y,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'thinking_time': self.thinking_time,
            'evaluation_score': self.evaluation_score,
            'strategy': self.strategy
        }

class GameStatistics(db.Model):
    """游戏统计表"""
    __tablename__ = 'game_statistics'
    
    id = Column(Integer, primary_key=True)
    date = Column(DateTime, default=datetime.utcnow)
    
    # 游戏数量统计
    total_games = Column(Integer, default=0)
    human_wins = Column(Integer, default=0)
    ai_wins = Column(Integer, default=0)
    draws = Column(Integer, default=0)
    
    # 难度统计
    easy_games = Column(Integer, default=0)
    medium_games = Column(Integer, default=0)
    hard_games = Column(Integer, default=0)
    
    # 平均游戏时长
    avg_game_duration = Column(Float)
    avg_moves_per_game = Column(Float)
    
    def __repr__(self):
        return f'<GameStatistics {self.date.date()}: {self.total_games} games>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'date': self.date.isoformat() if self.date else None,
            'total_games': self.total_games,
            'human_wins': self.human_wins,
            'ai_wins': self.ai_wins,
            'draws': self.draws,
            'easy_games': self.easy_games,
            'medium_games': self.medium_games,
            'hard_games': self.hard_games,
            'avg_game_duration': self.avg_game_duration,
            'avg_moves_per_game': self.avg_moves_per_game
        }
