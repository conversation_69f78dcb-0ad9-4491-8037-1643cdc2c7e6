"""
基于神经网络的AI代理
"""
import time
import numpy as np
from typing import <PERSON><PERSON>
from game.board import Board, Player
from ai.agents.base_agent import BaseAgent
from ai.model.manager import ModelManager

class NeuralAgent(BaseAgent):
    """基于神经网络的AI代理"""
    
    def __init__(self, player: Player, model_manager: ModelManager, 
                 temperature: float = 0.1):
        super().__init__(player, "NeuralAgent")
        self.model_manager = model_manager
        self.temperature = temperature
    
    def get_move(self, board: Board) -> Tuple[int, int]:
        """使用神经网络获取最佳移动"""
        start_time = time.time()
        
        if self.model_manager.current_model is None:
            # 如果没有模型，随机选择
            import random
            valid_moves = board.get_valid_moves()
            move = random.choice(valid_moves)
        else:
            # 获取棋盘状态
            board_state = board.get_state_for_ai(self.player)
            
            # 获取有效移动
            valid_moves = board.get_valid_moves()
            
            # 使用模型预测
            move = self.model_manager.get_best_move(
                board_state, valid_moves, self.temperature
            )
        
        # 更新统计
        thinking_time = time.time() - start_time
        self.move_count += 1
        self.total_thinking_time += thinking_time
        
        return move
    
    def get_policy_and_value(self, board: Board) -> Tuple[np.ndarray, float]:
        """获取策略和价值评估"""
        if self.model_manager.current_model is None:
            # 没有模型时返回均匀分布和0价值
            board_size = board.size
            uniform_policy = np.ones(board_size * board_size) / (board_size * board_size)
            return uniform_policy, 0.0
        
        board_state = board.get_state_for_ai(self.player)
        return self.model_manager.predict(board_state)
    
    def evaluate_position(self, board: Board) -> float:
        """评估当前局面"""
        if self.model_manager.current_model is None:
            return 0.0
        
        board_state = board.get_state_for_ai(self.player)
        return self.model_manager.evaluate_position(board_state)
