"""
游戏相关API端点
"""
from flask import request, jsonify
from app.api import api_bp
from app.models.game import Game, GameMove
from app import db
from game.engine import GameEngine
from datetime import datetime

@api_bp.route('/games', methods=['POST'])
def create_game():
    """创建新游戏"""
    try:
        data = request.get_json()
        player_type = data.get('player_type', 'human')  # human, ai
        difficulty = data.get('difficulty', 'medium')
        
        # 创建游戏实例
        game = Game(
            player_type=player_type,
            difficulty=difficulty,
            status='active',
            created_at=datetime.utcnow()
        )
        
        db.session.add(game)
        db.session.commit()
        
        return jsonify({
            'game_id': game.id,
            'status': 'created',
            'board_size': 15,
            'current_player': 'black'
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/games/<int:game_id>', methods=['GET'])
def get_game(game_id):
    """获取游戏状态"""
    try:
        game = Game.query.get_or_404(game_id)
        moves = GameMove.query.filter_by(game_id=game_id).order_by(GameMove.move_number).all()
        
        return jsonify({
            'game_id': game.id,
            'status': game.status,
            'player_type': game.player_type,
            'difficulty': game.difficulty,
            'current_player': game.current_player,
            'winner': game.winner,
            'moves': [
                {
                    'move_number': move.move_number,
                    'player': move.player,
                    'x': move.x,
                    'y': move.y,
                    'timestamp': move.timestamp.isoformat()
                } for move in moves
            ]
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/games/<int:game_id>/moves', methods=['POST'])
def make_move(game_id):
    """执行游戏移动"""
    try:
        game = Game.query.get_or_404(game_id)
        
        if game.status != 'active':
            return jsonify({'error': 'Game is not active'}), 400
        
        data = request.get_json()
        x = data.get('x')
        y = data.get('y')
        
        if x is None or y is None:
            return jsonify({'error': 'Missing x or y coordinate'}), 400
        
        # 验证移动是否有效
        engine = GameEngine(game_id)
        if not engine.is_valid_move(x, y):
            return jsonify({'error': 'Invalid move'}), 400
        
        # 执行移动
        result = engine.make_move(x, y, game.current_player)
        
        if result['success']:
            return jsonify({
                'success': True,
                'game_status': result['game_status'],
                'winner': result.get('winner'),
                'next_player': result.get('next_player'),
                'ai_move': result.get('ai_move')
            })
        else:
            return jsonify({'error': result['error']}), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/games/<int:game_id>/reset', methods=['POST'])
def reset_game(game_id):
    """重置游戏"""
    try:
        game = Game.query.get_or_404(game_id)
        
        # 删除所有移动记录
        GameMove.query.filter_by(game_id=game_id).delete()
        
        # 重置游戏状态
        game.status = 'active'
        game.current_player = 'black'
        game.winner = None
        game.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Game reset successfully'})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/games', methods=['GET'])
def list_games():
    """获取游戏列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        games = Game.query.order_by(Game.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'games': [
                {
                    'id': game.id,
                    'player_type': game.player_type,
                    'difficulty': game.difficulty,
                    'status': game.status,
                    'winner': game.winner,
                    'created_at': game.created_at.isoformat(),
                    'updated_at': game.updated_at.isoformat() if game.updated_at else None
                } for game in games.items
            ],
            'total': games.total,
            'pages': games.pages,
            'current_page': page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
