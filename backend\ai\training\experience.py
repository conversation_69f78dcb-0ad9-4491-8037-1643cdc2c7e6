"""
经验回放缓冲区
"""
import numpy as np
import random
from collections import deque
from typing import List, Tuple, Dict, Any
import pickle
import os

class Experience:
    """单个经验样本"""
    
    def __init__(self, state: np.ndarray, policy: np.ndarray, value: float, 
                 player: int, move: Tuple[int, int]):
        self.state = state  # 棋盘状态
        self.policy = policy  # MCTS策略概率
        self.value = value  # 游戏结果价值
        self.player = player  # 当前玩家
        self.move = move  # 实际移动
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'state': self.state,
            'policy': self.policy,
            'value': self.value,
            'player': self.player,
            'move': self.move
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Experience':
        """从字典创建"""
        return cls(
            data['state'],
            data['policy'],
            data['value'],
            data['player'],
            data['move']
        )

class ExperienceBuffer:
    """经验回放缓冲区"""
    
    def __init__(self, max_size: int = 100000):
        self.max_size = max_size
        self.buffer = deque(maxlen=max_size)
        self.game_buffers = []  # 临时存储当前游戏的经验
    
    def start_game(self):
        """开始新游戏"""
        self.game_buffers = []
    
    def add_experience(self, state: np.ndarray, policy: np.ndarray, 
                      player: int, move: Tuple[int, int]):
        """添加经验（游戏结束前暂存）"""
        experience = Experience(state, policy, 0.0, player, move)  # value稍后设置
        self.game_buffers.append(experience)
    
    def finish_game(self, winner: int):
        """游戏结束，设置价值并添加到缓冲区"""
        # 根据游戏结果设置每个经验的价值
        for i, exp in enumerate(self.game_buffers):
            if winner == 0:  # 平局
                exp.value = 0.0
            elif winner == exp.player:  # 获胜
                exp.value = 1.0
            else:  # 失败
                exp.value = -1.0
            
            # 添加到主缓冲区
            self.buffer.append(exp)
        
        # 清空游戏缓冲区
        self.game_buffers = []
    
    def sample_batch(self, batch_size: int) -> List[Experience]:
        """采样一批经验"""
        if len(self.buffer) < batch_size:
            return list(self.buffer)
        
        return random.sample(self.buffer, batch_size)
    
    def get_training_data(self, batch_size: int) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """获取训练数据"""
        batch = self.sample_batch(batch_size)
        
        if not batch:
            return np.array([]), np.array([]), np.array([])
        
        states = np.array([exp.state for exp in batch])
        policies = np.array([exp.policy for exp in batch])
        values = np.array([exp.value for exp in batch])
        
        return states, policies, values
    
    def size(self) -> int:
        """获取缓冲区大小"""
        return len(self.buffer)
    
    def clear(self):
        """清空缓冲区"""
        self.buffer.clear()
        self.game_buffers = []
    
    def save(self, filepath: str):
        """保存缓冲区到文件"""
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 转换为可序列化的格式
        data = {
            'max_size': self.max_size,
            'experiences': [exp.to_dict() for exp in self.buffer]
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(data, f)
    
    def load(self, filepath: str):
        """从文件加载缓冲区"""
        if not os.path.exists(filepath):
            return False
        
        try:
            with open(filepath, 'rb') as f:
                data = pickle.load(f)
            
            self.max_size = data['max_size']
            self.buffer = deque(maxlen=self.max_size)
            
            for exp_data in data['experiences']:
                exp = Experience.from_dict(exp_data)
                self.buffer.append(exp)
            
            return True
            
        except Exception as e:
            print(f"Error loading experience buffer: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓冲区统计信息"""
        if not self.buffer:
            return {
                'size': 0,
                'win_rate': 0.0,
                'draw_rate': 0.0,
                'loss_rate': 0.0
            }
        
        values = [exp.value for exp in self.buffer]
        wins = sum(1 for v in values if v > 0)
        draws = sum(1 for v in values if v == 0)
        losses = sum(1 for v in values if v < 0)
        total = len(values)
        
        return {
            'size': total,
            'win_rate': wins / total,
            'draw_rate': draws / total,
            'loss_rate': losses / total,
            'avg_value': np.mean(values)
        }

class PrioritizedExperienceBuffer(ExperienceBuffer):
    """优先级经验回放缓冲区"""
    
    def __init__(self, max_size: int = 100000, alpha: float = 0.6):
        super().__init__(max_size)
        self.alpha = alpha  # 优先级指数
        self.priorities = deque(maxlen=max_size)
        self.max_priority = 1.0
    
    def add_experience(self, state: np.ndarray, policy: np.ndarray, 
                      player: int, move: Tuple[int, int]):
        """添加经验（带优先级）"""
        super().add_experience(state, policy, player, move)
        # 新经验使用最大优先级
        if len(self.game_buffers) == 1:  # 第一个经验
            self.priorities.append(self.max_priority)
    
    def finish_game(self, winner: int):
        """游戏结束，更新优先级"""
        super().finish_game(winner)
        
        # 为新添加的经验设置优先级
        game_length = len(self.game_buffers)
        for i in range(game_length):
            if len(self.priorities) < len(self.buffer):
                self.priorities.append(self.max_priority)
    
    def sample_batch(self, batch_size: int, beta: float = 0.4) -> Tuple[List[Experience], np.ndarray, np.ndarray]:
        """基于优先级采样"""
        if len(self.buffer) < batch_size:
            indices = list(range(len(self.buffer)))
            weights = np.ones(len(self.buffer))
        else:
            # 计算采样概率
            priorities = np.array(self.priorities)
            probs = priorities ** self.alpha
            probs = probs / np.sum(probs)
            
            # 采样
            indices = np.random.choice(len(self.buffer), batch_size, p=probs)
            
            # 计算重要性采样权重
            weights = (len(self.buffer) * probs[indices]) ** (-beta)
            weights = weights / np.max(weights)
        
        experiences = [self.buffer[i] for i in indices]
        return experiences, indices, weights
    
    def update_priorities(self, indices: np.ndarray, td_errors: np.ndarray):
        """更新优先级"""
        for idx, error in zip(indices, td_errors):
            priority = (abs(error) + 1e-6) ** self.alpha
            self.priorities[idx] = priority
            self.max_priority = max(self.max_priority, priority)
