/**
 * 游戏配置文件
 */

const CONFIG = {
    // 棋盘配置
    BOARD: {
        SIZE: 15,
        CELL_SIZE: 30,
        MARGIN: 30,
        LINE_COLOR: '#8b4513',
        BACKGROUND_COLOR: '#deb887'
    },
    
    // 棋子配置
    STONE: {
        RADIUS: 12,
        BLACK_COLOR: '#000000',
        WHITE_COLOR: '#ffffff',
        BORDER_COLOR: '#333333',
        BORDER_WIDTH: 1
    },
    
    // 动画配置
    ANIMATION: {
        STONE_PLACE_DURATION: 300,
        STONE_HOVER_DURATION: 150,
        HINT_BLINK_DURATION: 1000,
        WINNING_PULSE_DURATION: 1000
    },
    
    // 游戏配置
    GAME: {
        WIN_LENGTH: 5,
        MAX_MOVES: 225, // 15x15
        THINKING_TIME_LIMIT: 30000, // 30秒
        AUTO_SAVE_INTERVAL: 10000 // 10秒
    },
    
    // API端点
    API: {
        BASE_URL: '/api',
        ENDPOINTS: {
            GAMES: '/games',
            MOVES: '/moves',
            HINTS: '/hints',
            MODELS: '/models',
            TRAINING: '/training'
        }
    },
    
    // WebSocket配置
    WEBSOCKET: {
        NAMESPACE: '/',
        EVENTS: {
            // 连接事件
            CONNECT: 'connect',
            DISCONNECT: 'disconnect',
            
            // 游戏事件
            JOIN_GAME: 'join_game',
            LEAVE_GAME: 'leave_game',
            MAKE_MOVE: 'make_move',
            GAME_JOINED: 'game_joined',
            MOVE_MADE: 'move_made',
            AI_MOVE: 'ai_move',
            GAME_FINISHED: 'game_finished',
            REQUEST_HINT: 'request_hint',
            HINT_RECEIVED: 'hint_received',
            SURRENDER: 'surrender',
            
            // 训练事件
            JOIN_TRAINING_MONITOR: 'join_training_monitor',
            TRAINING_UPDATE: 'training_update',
            TRAINING_PROGRESS: 'training_progress',
            TRAINING_COMPLETED: 'training_completed',
            
            // 错误事件
            ERROR: 'error'
        }
    },
    
    // UI配置
    UI: {
        TOAST_DURATION: 3000,
        MODAL_ANIMATION_DURATION: 300,
        LOADING_MIN_DURATION: 500,
        DEBOUNCE_DELAY: 300
    },
    
    // 音效配置
    SOUND: {
        ENABLED: true,
        VOLUME: 0.5,
        SOUNDS: {
            STONE_PLACE: '/static/assets/sounds/stone_place.mp3',
            GAME_WIN: '/static/assets/sounds/game_win.mp3',
            GAME_LOSE: '/static/assets/sounds/game_lose.mp3',
            BUTTON_CLICK: '/static/assets/sounds/button_click.mp3',
            ERROR: '/static/assets/sounds/error.mp3'
        }
    },
    
    // 主题配置
    THEME: {
        COLORS: {
            PRIMARY: '#3498db',
            SECONDARY: '#95a5a6',
            SUCCESS: '#27ae60',
            WARNING: '#f39c12',
            DANGER: '#e74c3c',
            INFO: '#17a2b8',
            LIGHT: '#ecf0f1',
            DARK: '#2c3e50'
        },
        GRADIENTS: {
            BLACK_STONE: 'radial-gradient(circle at 30% 30%, #666, #000)',
            WHITE_STONE: 'radial-gradient(circle at 30% 30%, #fff, #ddd)',
            BACKGROUND: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }
    },
    
    // 本地存储键名
    STORAGE_KEYS: {
        GAME_SETTINGS: 'gomoku_game_settings',
        GAME_HISTORY: 'gomoku_game_history',
        USER_PREFERENCES: 'gomoku_user_preferences',
        SOUND_SETTINGS: 'gomoku_sound_settings'
    },
    
    // 调试配置
    DEBUG: {
        ENABLED: false,
        LOG_LEVEL: 'info', // 'debug', 'info', 'warn', 'error'
        SHOW_COORDINATES: false,
        SHOW_MOVE_NUMBERS: false,
        PERFORMANCE_MONITORING: false
    }
};

// 冻结配置对象，防止意外修改
Object.freeze(CONFIG);

// 导出配置（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
