/**
 * 管理员页面JavaScript
 */

class AdminPanel {
    constructor() {
        this.currentSection = 'dashboard';
        this.charts = {};
        this.refreshIntervals = {};
        
        this.init();
    }
    
    init() {
        this.setupNavigation();
        this.setupEventHandlers();
        this.setupWebSocketEvents();
        this.loadDashboard();
        
        // 定期刷新数据
        this.startAutoRefresh();
    }
    
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                const section = link.dataset.section;
                this.switchSection(section);
                
                // 更新导航状态
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
            });
        });
    }
    
    switchSection(section) {
        // 隐藏所有内容区域
        document.querySelectorAll('.content-section').forEach(s => {
            s.classList.remove('active');
        });
        
        // 显示目标区域
        const targetSection = document.getElementById(`${section}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = section;
            
            // 更新页面标题
            this.updatePageTitle(section);
            
            // 加载对应数据
            this.loadSectionData(section);
        }
    }
    
    updatePageTitle(section) {
        const titles = {
            dashboard: '系统概览',
            training: '训练管理',
            models: '模型管理',
            evaluation: '性能评估',
            games: '游戏记录',
            settings: '系统设置'
        };
        
        document.getElementById('pageTitle').textContent = titles[section] || '管理面板';
    }
    
    setupEventHandlers() {
        // 训练管理
        document.getElementById('startTrainingBtn').addEventListener('click', () => {
            this.showTrainingConfig();
        });
        
        document.getElementById('stopTrainingBtn').addEventListener('click', () => {
            this.stopTraining();
        });
        
        document.getElementById('trainingForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startTraining();
        });
        
        document.getElementById('cancelTraining').addEventListener('click', () => {
            this.hideTrainingConfig();
        });
        
        // 模型管理
        document.getElementById('refreshModels').addEventListener('click', () => {
            this.loadModels();
        });
        
        // 评估管理
        document.getElementById('startEvaluation').addEventListener('click', () => {
            this.showEvaluationConfig();
        });
        
        document.getElementById('evaluationForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startEvaluation();
        });
        
        document.getElementById('cancelEvaluation').addEventListener('click', () => {
            this.hideEvaluationConfig();
        });
        
        // 游戏记录
        document.getElementById('refreshGames').addEventListener('click', () => {
            this.loadGames();
        });
        
        // 设置
        document.getElementById('saveSettings').addEventListener('click', () => {
            this.saveSettings();
        });
        
        document.getElementById('backupData').addEventListener('click', () => {
            this.backupData();
        });
        
        document.getElementById('cleanupData').addEventListener('click', () => {
            this.cleanupData();
        });
        
        document.getElementById('resetSystem').addEventListener('click', () => {
            this.resetSystem();
        });
    }
    
    setupWebSocketEvents() {
        // 训练进度更新
        wsManager.on('trainingProgress', (data) => {
            this.updateTrainingProgress(data);
        });
        
        // 训练完成
        wsManager.on('trainingCompleted', (data) => {
            this.onTrainingCompleted(data);
        });
        
        // 训练错误
        wsManager.on('error', (data) => {
            this.showError(data.message);
        });
        
        // 加入训练监控
        wsManager.joinTrainingMonitor();
    }
    
    async loadDashboard() {
        try {
            // 加载统计数据
            const stats = await this.fetchAPI('/api/stats');
            this.updateDashboardStats(stats);
            
            // 加载性能图表
            this.loadPerformanceChart();
            
            // 检查当前训练状态
            this.checkCurrentTraining();
            
        } catch (error) {
            console.error('Failed to load dashboard:', error);
        }
    }
    
    updateDashboardStats(stats) {
        document.getElementById('totalSessions').textContent = stats.training_sessions || 0;
        document.getElementById('totalModels').textContent = stats.models || 0;
        document.getElementById('totalGames').textContent = stats.games || 0;
        document.getElementById('aiWinRate').textContent = `${(stats.ai_win_rate * 100).toFixed(1)}%`;
    }
    
    async loadPerformanceChart() {
        try {
            const data = await this.fetchAPI('/api/training/metrics');
            
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            if (this.charts.performance) {
                this.charts.performance.destroy();
            }
            
            this.charts.performance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.epochs || [],
                    datasets: [{
                        label: '胜率',
                        data: data.win_rates || [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    }, {
                        label: '损失',
                        data: data.losses || [],
                        borderColor: '#e74c3c',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '胜率'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '损失'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
            
        } catch (error) {
            console.error('Failed to load performance chart:', error);
        }
    }
    
    async checkCurrentTraining() {
        try {
            const status = await this.fetchAPI('/api/training/status');
            
            if (status.is_training) {
                this.showCurrentTraining(status);
                document.getElementById('stopTrainingBtn').disabled = false;
            } else {
                this.hideCurrentTraining();
                document.getElementById('stopTrainingBtn').disabled = true;
            }
            
        } catch (error) {
            console.error('Failed to check training status:', error);
        }
    }
    
    showCurrentTraining(status) {
        const container = document.getElementById('currentTraining');
        
        const progressPercent = (status.current_epoch / status.total_epochs * 100).toFixed(1);
        
        container.innerHTML = `
            <div class="training-info">
                <div class="training-header">
                    <h4>训练会话 #${status.session_id}</h4>
                    <span class="status-badge running">进行中</span>
                </div>
                <div class="progress-info">
                    <div class="progress-text">
                        轮次: ${status.current_epoch} / ${status.total_epochs} (${progressPercent}%)
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progressPercent}%"></div>
                    </div>
                </div>
                <div class="training-metrics">
                    <div class="metric">
                        <span class="metric-label">当前损失:</span>
                        <span class="metric-value">${status.current_loss.toFixed(4)}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">胜率:</span>
                        <span class="metric-value">${(status.win_rate * 100).toFixed(1)}%</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">已用时间:</span>
                        <span class="metric-value">${this.formatTime(status.elapsed_time)}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">预计剩余:</span>
                        <span class="metric-value">${this.formatTime(status.estimated_remaining)}</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    hideCurrentTraining() {
        const container = document.getElementById('currentTraining');
        container.innerHTML = '<div class="no-training">暂无进行中的训练</div>';
    }
    
    updateTrainingProgress(data) {
        if (this.currentSection === 'dashboard') {
            this.showCurrentTraining(data);
        }
        
        // 更新性能图表
        if (this.charts.performance && data.current_epoch % 10 === 0) {
            this.loadPerformanceChart();
        }
    }
    
    onTrainingCompleted(data) {
        this.hideCurrentTraining();
        document.getElementById('stopTrainingBtn').disabled = true;
        
        this.showSuccess(`训练完成！最终胜率: ${(data.final_win_rate * 100).toFixed(1)}%`);
        
        // 刷新相关数据
        this.loadDashboard();
        if (this.currentSection === 'training') {
            this.loadTrainingSessions();
        }
        if (this.currentSection === 'models') {
            this.loadModels();
        }
    }
    
    showTrainingConfig() {
        document.getElementById('trainingConfig').style.display = 'block';
        
        // 生成默认模型名称
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '').replace('T', '_');
        document.getElementById('modelName').value = `model_${timestamp}`;
    }
    
    hideTrainingConfig() {
        document.getElementById('trainingConfig').style.display = 'none';
    }
    
    async startTraining() {
        const formData = new FormData(document.getElementById('trainingForm'));
        const config = Object.fromEntries(formData.entries());
        
        // 转换数值类型
        config.epochs = parseInt(config.epochs);
        config.batch_size = parseInt(config.batchSize);
        config.learning_rate = parseFloat(config.learningRate);
        config.self_play_games = parseInt(config.selfPlayGames);
        
        try {
            await this.fetchAPI('/api/training/start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(config)
            });
            
            this.hideTrainingConfig();
            this.showSuccess('训练已开始');
            
            // 刷新状态
            setTimeout(() => this.checkCurrentTraining(), 1000);
            
        } catch (error) {
            this.showError('启动训练失败: ' + error.message);
        }
    }
    
    async stopTraining() {
        if (!confirm('确定要停止当前训练吗？')) return;
        
        try {
            await this.fetchAPI('/api/training/stop', { method: 'POST' });
            this.showSuccess('训练已停止');
            
        } catch (error) {
            this.showError('停止训练失败: ' + error.message);
        }
    }
    
    loadSectionData(section) {
        switch (section) {
            case 'training':
                this.loadTrainingSessions();
                break;
            case 'models':
                this.loadModels();
                break;
            case 'evaluation':
                this.loadEvaluations();
                break;
            case 'games':
                this.loadGames();
                break;
            case 'settings':
                this.loadSettings();
                break;
        }
    }
    
    async fetchAPI(url, options = {}) {
        const response = await fetch(url, options);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    }
    
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }
    
    showSuccess(message) {
        // 实现成功消息显示
        console.log('Success:', message);
    }
    
    showError(message) {
        // 实现错误消息显示
        console.error('Error:', message);
    }
    
    startAutoRefresh() {
        // 每30秒刷新一次概览数据
        this.refreshIntervals.dashboard = setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.loadDashboard();
            }
        }, 30000);
    }
    
    // 占位方法，需要进一步实现
    async loadTrainingSessions() { /* TODO */ }
    async loadModels() { /* TODO */ }
    async loadEvaluations() { /* TODO */ }
    async loadGames() { /* TODO */ }
    async loadSettings() { /* TODO */ }
    showEvaluationConfig() { /* TODO */ }
    hideEvaluationConfig() { /* TODO */ }
    startEvaluation() { /* TODO */ }
    saveSettings() { /* TODO */ }
    backupData() { /* TODO */ }
    cleanupData() { /* TODO */ }
    resetSystem() { /* TODO */ }
}

// 初始化管理面板
document.addEventListener('DOMContentLoaded', () => {
    new AdminPanel();
});
