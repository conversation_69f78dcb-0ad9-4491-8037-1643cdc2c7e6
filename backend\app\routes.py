"""
主要路由定义
"""
from flask import render_template, current_app
from app import db

@current_app.route('/')
def index():
    """游戏主页"""
    return render_template('index.html')

@current_app.route('/admin')
def admin():
    """管理员控制面板"""
    return render_template('admin.html')

@current_app.route('/health')
def health_check():
    """健康检查端点"""
    return {'status': 'healthy', 'message': 'AI Gomoku System is running'}

@current_app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return {'error': 'Not found'}, 404

@current_app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    db.session.rollback()
    return {'error': 'Internal server error'}, 500
