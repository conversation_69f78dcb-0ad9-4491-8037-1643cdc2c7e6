<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI五子棋对战系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1>AI五子棋对战系统</h1>
            <div class="game-controls">
                <button id="newGameBtn" class="btn btn-primary">新游戏</button>
                <button id="surrenderBtn" class="btn btn-secondary" disabled>投降</button>
                <button id="hintBtn" class="btn btn-info" disabled>提示</button>
                <button id="undoBtn" class="btn btn-warning" disabled>悔棋</button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 游戏设置面板 -->
            <div class="game-setup" id="gameSetup">
                <h2>游戏设置</h2>
                <div class="setup-form">
                    <div class="form-group">
                        <label for="playerType">对手类型:</label>
                        <select id="playerType" class="form-control">
                            <option value="ai">AI对手</option>
                            <option value="human">人类对手</option>
                        </select>
                    </div>
                    <div class="form-group" id="difficultyGroup">
                        <label for="difficulty">AI难度:</label>
                        <select id="difficulty" class="form-control">
                            <option value="easy">简单</option>
                            <option value="medium" selected>中等</option>
                            <option value="hard">困难</option>
                        </select>
                    </div>
                    <button id="startGameBtn" class="btn btn-success">开始游戏</button>
                </div>
            </div>

            <!-- 游戏区域 -->
            <div class="game-area" id="gameArea" style="display: none;">
                <!-- 游戏信息 -->
                <div class="game-info">
                    <div class="player-info">
                        <div class="player black-player">
                            <div class="player-stone black-stone"></div>
                            <span class="player-name">黑棋 (您)</span>
                            <div class="player-status" id="blackStatus"></div>
                        </div>
                        <div class="game-status">
                            <div class="current-turn" id="currentTurn">黑棋先行</div>
                            <div class="move-count">步数: <span id="moveCount">0</span></div>
                            <div class="game-time">用时: <span id="gameTime">00:00</span></div>
                        </div>
                        <div class="player white-player">
                            <div class="player-stone white-stone"></div>
                            <span class="player-name" id="whitePlayerName">白棋 (AI)</span>
                            <div class="player-status" id="whiteStatus"></div>
                        </div>
                    </div>
                </div>

                <!-- 棋盘容器 -->
                <div class="board-container">
                    <svg id="gameBoard" class="game-board"></svg>
                    <div class="board-overlay" id="boardOverlay"></div>
                </div>

                <!-- 移动历史 -->
                <div class="move-history">
                    <h3>移动历史</h3>
                    <div class="history-list" id="historyList"></div>
                </div>
            </div>
        </main>

        <!-- 游戏结果模态框 -->
        <div class="modal" id="gameResultModal">
            <div class="modal-content">
                <h2 id="resultTitle">游戏结束</h2>
                <p id="resultMessage"></p>
                <div class="modal-actions">
                    <button id="newGameFromResult" class="btn btn-primary">再来一局</button>
                    <button id="backToSetup" class="btn btn-secondary">返回设置</button>
                </div>
            </div>
        </div>

        <!-- 提示模态框 -->
        <div class="modal" id="hintModal">
            <div class="modal-content">
                <h3>AI建议</h3>
                <p id="hintMessage"></p>
                <div class="hint-position">
                    <span>建议位置: </span>
                    <span id="hintPosition"></span>
                </div>
                <div class="hint-confidence">
                    <span>置信度: </span>
                    <span id="hintConfidence"></span>
                </div>
                <button id="closeHint" class="btn btn-primary">知道了</button>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div class="loading" id="loadingIndicator">
            <div class="spinner"></div>
            <p id="loadingMessage">正在思考中...</p>
        </div>

        <!-- 连接状态 -->
        <div class="connection-status" id="connectionStatus">
            <span class="status-indicator" id="statusIndicator"></span>
            <span id="statusText">连接中...</span>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="{{ url_for('static', filename='js/config.js') }}"></script>
    <script src="{{ url_for('static', filename='js/websocket.js') }}"></script>
    <script src="{{ url_for('static', filename='js/board.js') }}"></script>
    <script src="{{ url_for('static', filename='js/game.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ui.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
