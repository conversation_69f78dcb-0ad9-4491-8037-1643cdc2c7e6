"""
五子棋AI神经网络模型
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple

class GomokuNet(nn.Module):
    """五子棋神经网络基类"""
    
    def __init__(self, board_size: int = 15, input_channels: int = 3):
        """
        初始化网络
        
        Args:
            board_size: 棋盘大小
            input_channels: 输入通道数（当前玩家、对手、空位）
        """
        super(GomokuNet, self).__init__()
        self.board_size = board_size
        self.input_channels = input_channels
        self.output_size = board_size * board_size
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入张量 (batch_size, input_channels, board_size, board_size)
            
        Returns:
            Tuple[torch.Tensor, torch.Tensor]: (策略输出, 价值输出)
        """
        raise NotImplementedError

class CNNGomoku(GomokuNet):
    """基于CNN的五子棋网络"""
    
    def __init__(self, board_size: int = 15, input_channels: int = 3, hidden_size: int = 256):
        super(CNNGomoku, self).__init__(board_size, input_channels)
        
        self.hidden_size = hidden_size
        
        # 卷积层
        self.conv1 = nn.Conv2d(input_channels, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
        
        # 批归一化
        self.bn1 = nn.BatchNorm2d(32)
        self.bn2 = nn.BatchNorm2d(64)
        self.bn3 = nn.BatchNorm2d(128)
        self.bn4 = nn.BatchNorm2d(256)
        
        # 全连接层
        self.fc_input_size = 256 * board_size * board_size
        self.fc1 = nn.Linear(self.fc_input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        
        # 策略头（输出每个位置的概率）
        self.policy_head = nn.Linear(hidden_size, self.output_size)
        
        # 价值头（输出局面评估）
        self.value_head = nn.Linear(hidden_size, 1)
        
        # Dropout
        self.dropout = nn.Dropout(0.3)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        # 卷积层
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = F.relu(self.bn4(self.conv4(x)))
        
        # 展平
        x = x.view(x.size(0), -1)
        
        # 全连接层
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        
        # 输出头
        policy = self.policy_head(x)
        value = torch.tanh(self.value_head(x))
        
        return policy, value

class ResidualBlock(nn.Module):
    """残差块"""
    
    def __init__(self, channels: int):
        super(ResidualBlock, self).__init__()
        
        self.conv1 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn1 = nn.BatchNorm2d(channels)
        self.conv2 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn2 = nn.BatchNorm2d(channels)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        residual = x
        
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        
        out += residual
        out = F.relu(out)
        
        return out

class ResNetGomoku(GomokuNet):
    """基于ResNet的五子棋网络"""
    
    def __init__(self, board_size: int = 15, input_channels: int = 3, 
                 num_blocks: int = 10, channels: int = 256):
        super(ResNetGomoku, self).__init__(board_size, input_channels)
        
        self.num_blocks = num_blocks
        self.channels = channels
        
        # 初始卷积层
        self.conv_input = nn.Conv2d(input_channels, channels, kernel_size=3, padding=1)
        self.bn_input = nn.BatchNorm2d(channels)
        
        # 残差块
        self.res_blocks = nn.ModuleList([
            ResidualBlock(channels) for _ in range(num_blocks)
        ])
        
        # 策略头
        self.policy_conv = nn.Conv2d(channels, 2, kernel_size=1)
        self.policy_bn = nn.BatchNorm2d(2)
        self.policy_fc = nn.Linear(2 * board_size * board_size, self.output_size)
        
        # 价值头
        self.value_conv = nn.Conv2d(channels, 1, kernel_size=1)
        self.value_bn = nn.BatchNorm2d(1)
        self.value_fc1 = nn.Linear(board_size * board_size, channels)
        self.value_fc2 = nn.Linear(channels, 1)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        # 初始卷积
        x = F.relu(self.bn_input(self.conv_input(x)))
        
        # 残差块
        for block in self.res_blocks:
            x = block(x)
        
        # 策略头
        policy = F.relu(self.policy_bn(self.policy_conv(x)))
        policy = policy.view(policy.size(0), -1)
        policy = self.policy_fc(policy)
        
        # 价值头
        value = F.relu(self.value_bn(self.value_conv(x)))
        value = value.view(value.size(0), -1)
        value = F.relu(self.value_fc1(value))
        value = torch.tanh(self.value_fc2(value))
        
        return policy, value

class AttentionBlock(nn.Module):
    """注意力块"""
    
    def __init__(self, channels: int, num_heads: int = 8):
        super(AttentionBlock, self).__init__()
        
        self.channels = channels
        self.num_heads = num_heads
        self.head_dim = channels // num_heads
        
        self.query = nn.Linear(channels, channels)
        self.key = nn.Linear(channels, channels)
        self.value = nn.Linear(channels, channels)
        
        self.out_proj = nn.Linear(channels, channels)
        self.norm1 = nn.LayerNorm(channels)
        self.norm2 = nn.LayerNorm(channels)
        
        self.ffn = nn.Sequential(
            nn.Linear(channels, channels * 4),
            nn.ReLU(),
            nn.Linear(channels * 4, channels)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x shape: (batch_size, seq_len, channels)
        batch_size, seq_len, _ = x.shape
        
        # 自注意力
        residual = x
        x = self.norm1(x)
        
        q = self.query(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.key(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.value(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # 计算注意力
        scores = torch.matmul(q, k.transpose(-2, -1)) / (self.head_dim ** 0.5)
        attn_weights = F.softmax(scores, dim=-1)
        attn_output = torch.matmul(attn_weights, v)
        
        # 合并头
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.channels
        )
        
        x = residual + self.out_proj(attn_output)
        
        # 前馈网络
        residual = x
        x = self.norm2(x)
        x = residual + self.ffn(x)
        
        return x

class TransformerGomoku(GomokuNet):
    """基于Transformer的五子棋网络"""
    
    def __init__(self, board_size: int = 15, input_channels: int = 3,
                 d_model: int = 256, num_layers: int = 6, num_heads: int = 8):
        super(TransformerGomoku, self).__init__(board_size, input_channels)
        
        self.d_model = d_model
        self.num_layers = num_layers
        
        # 输入嵌入
        self.input_proj = nn.Linear(input_channels, d_model)
        self.pos_embedding = nn.Parameter(torch.randn(1, board_size * board_size, d_model))
        
        # Transformer层
        self.transformer_blocks = nn.ModuleList([
            AttentionBlock(d_model, num_heads) for _ in range(num_layers)
        ])
        
        # 输出头
        self.policy_head = nn.Linear(d_model, 1)
        self.value_head = nn.Linear(d_model, 1)
        
        self.norm = nn.LayerNorm(d_model)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        batch_size = x.size(0)
        
        # 重塑输入: (batch, channels, height, width) -> (batch, seq_len, channels)
        x = x.permute(0, 2, 3, 1).contiguous()  # (batch, height, width, channels)
        x = x.view(batch_size, -1, self.input_channels)  # (batch, seq_len, channels)
        
        # 输入投影和位置编码
        x = self.input_proj(x)
        x = x + self.pos_embedding
        
        # Transformer层
        for block in self.transformer_blocks:
            x = block(x)
        
        x = self.norm(x)
        
        # 策略头：每个位置的概率
        policy = self.policy_head(x).squeeze(-1)  # (batch, seq_len)
        
        # 价值头：全局池化后输出单个值
        value = torch.mean(x, dim=1)  # (batch, d_model)
        value = torch.tanh(self.value_head(value))  # (batch, 1)
        
        return policy, value
