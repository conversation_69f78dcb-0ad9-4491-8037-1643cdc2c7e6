/* 管理员页面样式 */

.admin-container {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.5em;
    font-weight: 600;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-left-color: #3498db;
}

.nav-link .icon {
    margin-right: 10px;
    font-size: 1.2em;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.admin-header {
    background: white;
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-header h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 2em;
    font-weight: 600;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 内容区域 */
.content-section {
    display: none;
    padding: 30px;
    flex: 1;
    overflow-y: auto;
}

.content-section.active {
    display: block;
}

/* 概览页面 */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto 1fr;
    gap: 20px;
    height: 100%;
}

.stats-cards {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2.5em;
    opacity: 0.8;
}

.stat-content h3 {
    margin: 0 0 5px 0;
    color: #7f8c8d;
    font-size: 0.9em;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-number {
    font-size: 2em;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-label {
    color: #95a5a6;
    font-size: 0.8em;
}

.training-progress-card,
.chart-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.training-progress-card h3,
.chart-card h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.2em;
    font-weight: 600;
}

.no-training {
    text-align: center;
    color: #95a5a6;
    padding: 40px 20px;
    font-style: italic;
}

/* 表单样式 */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 8px;
    font-weight: 600;
    color: #34495e;
}

.form-control {
    padding: 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* 训练管理 */
.training-controls {
    margin-bottom: 30px;
    display: flex;
    gap: 10px;
}

.training-config {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.training-config h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
}

.training-history {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.training-history h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.data-table .status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.running {
    background: #d4edda;
    color: #155724;
}

.status-badge.completed {
    background: #cce5ff;
    color: #004085;
}

.status-badge.failed {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.stopped {
    background: #fff3cd;
    color: #856404;
}

/* 模型管理 */
.models-controls {
    margin-bottom: 30px;
    display: flex;
    gap: 10px;
}

.models-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.models-list,
.model-details {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.models-list h3,
.model-details h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
}

/* 评估页面 */
.evaluation-controls {
    margin-bottom: 30px;
    display: flex;
    gap: 10px;
}

.evaluation-config {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.evaluation-config h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
}

.evaluation-results {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.evaluation-results h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
}

/* 游戏记录 */
.games-controls {
    margin-bottom: 30px;
    display: flex;
    gap: 10px;
}

/* 设置页面 */
.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.settings-group {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.settings-group h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
}

.setting-item {
    margin-bottom: 20px;
}

.setting-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #34495e;
}

.setting-description {
    margin: 8px 0 0 0;
    color: #7f8c8d;
    font-size: 0.9em;
}

.settings-actions {
    text-align: center;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
    }
    
    .sidebar-nav {
        padding: 10px 0;
    }
    
    .sidebar-nav ul {
        display: flex;
        overflow-x: auto;
    }
    
    .sidebar-nav li {
        margin-bottom: 0;
        margin-right: 5px;
        flex-shrink: 0;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .models-grid {
        grid-template-columns: 1fr;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
}
