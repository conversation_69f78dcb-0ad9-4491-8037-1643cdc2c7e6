"""
随机AI代理
"""
import random
import time
from typing import <PERSON><PERSON>
from game.board import Board, Player
from ai.agents.base_agent import BaseAgent

class RandomAgent(BaseAgent):
    """随机AI代理"""
    
    def __init__(self, player: Player):
        super().__init__(player, "RandomAgent")
    
    def get_move(self, board: Board) -> Tuple[int, int]:
        """随机选择一个有效移动"""
        start_time = time.time()
        
        valid_moves = board.get_valid_moves()
        if not valid_moves:
            raise ValueError("No valid moves available")
        
        move = random.choice(valid_moves)
        
        # 更新统计
        thinking_time = time.time() - start_time
        self.move_count += 1
        self.total_thinking_time += thinking_time
        
        return move
