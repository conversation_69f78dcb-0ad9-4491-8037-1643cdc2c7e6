"""
AI训练器
"""
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import time
import os
from typing import Dict, Any, Optional
from datetime import datetime

from ai.model.manager import ModelManager
from ai.training.experience import ExperienceBuffer
from ai.training.self_play import SelfPlayWorker
from app.models.training import TrainingSession, TrainingConfig, TrainingMetric
from app.websocket.training_events import (
    broadcast_training_progress, broadcast_training_completed, 
    broadcast_training_error, broadcast_self_play_game
)
from app import db

class AITrainer:
    """AI训练器"""
    
    def __init__(self, session_id: int):
        self.session_id = session_id
        self.is_training = False
        self.should_stop = False
        
        # 组件
        self.model_manager = ModelManager()
        self.experience_buffer = ExperienceBuffer()
        self.self_play_worker = None
        
        # 训练状态
        self.current_epoch = 0
        self.total_epochs = 0
        self.current_loss = 0.0
        self.best_loss = float('inf')
        self.win_rate = 0.0
        self.games_played = 0
        
        # 时间统计
        self.start_time = None
        self.epoch_start_time = None
        
        # 设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    def start_training(self, config: TrainingConfig):
        """开始训练"""
        try:
            self.is_training = True
            self.should_stop = False
            self.start_time = time.time()
            self.total_epochs = config.epochs
            
            # 更新会话状态
            session = TrainingSession.query.get(self.session_id)
            session.status = 'running'
            session.total_epochs = config.epochs
            db.session.commit()
            
            # 创建模型
            model = self.model_manager.create_model(
                config.model_architecture,
                board_size=15,
                input_channels=3,
                hidden_size=config.hidden_size,
                num_blocks=config.num_layers
            )
            
            # 设置优化器
            optimizer = optim.Adam(model.parameters(), lr=config.learning_rate)
            scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=100, gamma=0.9)
            
            # 创建自我对弈工作器
            self.self_play_worker = SelfPlayWorker(
                self.model_manager, 
                self.experience_buffer,
                simulations=400
            )
            
            # 训练循环
            for epoch in range(config.epochs):
                if self.should_stop:
                    break
                
                self.current_epoch = epoch + 1
                self.epoch_start_time = time.time()
                
                # 自我对弈生成数据
                if epoch % 10 == 0 or self.experience_buffer.size() < config.batch_size:
                    self._generate_self_play_data(config.self_play_games)
                
                # 训练模型
                if self.experience_buffer.size() >= config.batch_size:
                    loss = self._train_epoch(model, optimizer, config.batch_size)
                    self.current_loss = loss
                    
                    if loss < self.best_loss:
                        self.best_loss = loss
                        self._save_checkpoint(model, epoch, 'best')
                
                # 更新学习率
                scheduler.step()
                
                # 评估模型
                if epoch % config.evaluation_interval == 0:
                    self._evaluate_model()
                
                # 保存检查点
                if epoch % config.save_interval == 0:
                    self._save_checkpoint(model, epoch)
                
                # 记录指标
                self._record_metrics(epoch, optimizer.param_groups[0]['lr'])
                
                # 广播进度
                self._broadcast_progress()
            
            # 训练完成
            self._finish_training(model, config)
            
        except Exception as e:
            self._handle_training_error(str(e))
    
    def stop_training(self):
        """停止训练"""
        self.should_stop = True
        
        # 更新会话状态
        session = TrainingSession.query.get(self.session_id)
        session.status = 'stopped'
        session.completed_at = datetime.utcnow()
        db.session.commit()
    
    def _generate_self_play_data(self, num_games: int):
        """生成自我对弈数据"""
        def game_callback(game_num, total_games, game_result):
            self.games_played += 1
            
            # 广播游戏数据
            broadcast_self_play_game({
                'game_id': game_result['game_id'],
                'moves': len(game_result['game_data']),
                'winner': game_result['winner_name'],
                'game_length': game_result['move_count'],
                'evaluation_score': 0.0  # 可以添加评估分数
            })
        
        # 进行自我对弈
        results = self.self_play_worker.play_games(num_games, game_callback)
        
        # 计算胜率
        stats = self.self_play_worker.get_statistics(results)
        self.win_rate = stats.get('black_win_rate', 0.5)  # 使用黑棋胜率作为参考
    
    def _train_epoch(self, model: nn.Module, optimizer: optim.Optimizer, 
                    batch_size: int) -> float:
        """训练一个epoch"""
        model.train()
        
        # 获取训练数据
        states, target_policies, target_values = self.experience_buffer.get_training_data(batch_size)
        
        if len(states) == 0:
            return 0.0
        
        # 转换为张量
        states = torch.FloatTensor(states).to(self.device)
        target_policies = torch.FloatTensor(target_policies).to(self.device)
        target_values = torch.FloatTensor(target_values).to(self.device)
        
        # 前向传播
        pred_policies, pred_values = model(states)
        
        # 计算损失
        policy_loss = nn.CrossEntropyLoss()(pred_policies, target_policies)
        value_loss = nn.MSELoss()(pred_values.squeeze(), target_values)
        total_loss = policy_loss + value_loss
        
        # 反向传播
        optimizer.zero_grad()
        total_loss.backward()
        optimizer.step()
        
        return total_loss.item()
    
    def _evaluate_model(self):
        """评估模型性能"""
        # 这里可以实现模型评估逻辑
        # 例如与之前的模型对战，或与随机代理对战
        pass
    
    def _save_checkpoint(self, model: nn.Module, epoch: int, suffix: str = ''):
        """保存模型检查点"""
        checkpoint_dir = f'data/models/session_{self.session_id}'
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        filename = f'model_epoch_{epoch}'
        if suffix:
            filename += f'_{suffix}'
        filename += '.pth'
        
        filepath = os.path.join(checkpoint_dir, filename)
        
        # 保存模型
        self.model_manager.save_model(
            model, 
            f'session_{self.session_id}_model',
            f'epoch_{epoch}',
            f'Model from training session {self.session_id}, epoch {epoch}',
            self.session_id
        )
    
    def _record_metrics(self, epoch: int, learning_rate: float):
        """记录训练指标"""
        metric = TrainingMetric(
            session_id=self.session_id,
            epoch=epoch,
            loss=self.current_loss,
            win_rate=self.win_rate,
            learning_rate=learning_rate,
            timestamp=datetime.utcnow()
        )
        
        db.session.add(metric)
        db.session.commit()
    
    def _broadcast_progress(self):
        """广播训练进度"""
        elapsed_time = time.time() - self.start_time
        epoch_time = time.time() - self.epoch_start_time if self.epoch_start_time else 0
        
        # 估算剩余时间
        if self.current_epoch > 0:
            avg_epoch_time = elapsed_time / self.current_epoch
            remaining_epochs = self.total_epochs - self.current_epoch
            estimated_remaining = avg_epoch_time * remaining_epochs
        else:
            estimated_remaining = 0
        
        progress_data = {
            'current_epoch': self.current_epoch,
            'total_epochs': self.total_epochs,
            'current_loss': self.current_loss,
            'win_rate': self.win_rate,
            'games_played': self.games_played,
            'elapsed_time': elapsed_time,
            'estimated_remaining': estimated_remaining
        }
        
        broadcast_training_progress(self.session_id, progress_data)
    
    def _finish_training(self, model: nn.Module, config: TrainingConfig):
        """完成训练"""
        self.is_training = False
        
        # 保存最终模型
        final_model = self.model_manager.save_model(
            model,
            config.model_name,
            'final',
            f'Final model from training session {self.session_id}',
            self.session_id
        )
        
        # 更新会话状态
        session = TrainingSession.query.get(self.session_id)
        session.status = 'completed'
        session.completed_at = datetime.utcnow()
        session.current_epoch = self.current_epoch
        session.final_loss = self.current_loss
        session.final_win_rate = self.win_rate
        session.total_games_played = self.games_played
        session.total_training_time = time.time() - self.start_time
        
        db.session.commit()
        
        # 广播完成事件
        broadcast_training_completed(self.session_id, {
            'final_loss': self.current_loss,
            'final_win_rate': self.win_rate,
            'total_epochs': self.current_epoch,
            'model_path': final_model.file_path
        })
    
    def _handle_training_error(self, error_message: str):
        """处理训练错误"""
        self.is_training = False
        
        # 更新会话状态
        session = TrainingSession.query.get(self.session_id)
        session.status = 'failed'
        session.completed_at = datetime.utcnow()
        session.error_message = error_message
        
        db.session.commit()
        
        # 广播错误事件
        broadcast_training_error(self.session_id, error_message)
    
    def get_status(self) -> Dict[str, Any]:
        """获取训练状态"""
        elapsed_time = time.time() - self.start_time if self.start_time else 0
        
        if self.current_epoch > 0 and self.start_time:
            avg_epoch_time = elapsed_time / self.current_epoch
            remaining_epochs = self.total_epochs - self.current_epoch
            estimated_remaining = avg_epoch_time * remaining_epochs
        else:
            estimated_remaining = 0
        
        return {
            'current_epoch': self.current_epoch,
            'total_epochs': self.total_epochs,
            'current_loss': self.current_loss,
            'win_rate': self.win_rate,
            'games_played': self.games_played,
            'elapsed_time': elapsed_time,
            'estimated_remaining': estimated_remaining
        }
