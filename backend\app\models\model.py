"""
AI模型相关数据模型
"""
from app import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Boolean

class AIModel(db.Model):
    """AI模型表"""
    __tablename__ = 'ai_models'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    version = Column(String(20), nullable=False)
    description = Column(Text)
    
    # 模型状态
    status = Column(String(20), default='training')  # 'training', 'ready', 'deprecated', 'failed'
    is_active = Column(Boolean, default=False)  # 是否为当前激活的模型
    
    # 模型文件信息
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)  # 文件大小（字节）
    checksum = Column(String(64))  # 文件校验和
    
    # 模型架构信息
    architecture = Column(String(50))  # 'resnet', 'cnn', 'transformer'
    input_size = Column(String(20))  # 输入尺寸，如 '15x15'
    hidden_size = Column(Integer)
    num_layers = Column(Integer)
    total_parameters = Column(Integer)  # 总参数数量
    
    # 性能指标
    performance_score = Column(Float)  # 综合性能分数
    win_rate = Column(Float)  # 胜率
    draw_rate = Column(Float)  # 平局率
    avg_thinking_time = Column(Float)  # 平均思考时间
    
    # 训练信息
    training_session_id = Column(Integer, db.ForeignKey('training_sessions.id'))
    training_epochs = Column(Integer)
    training_loss = Column(Float)
    training_time = Column(Float)  # 训练时间（小时）
    
    # 评估信息
    total_games = Column(Integer, default=0)  # 总对局数
    wins = Column(Integer, default=0)
    losses = Column(Integer, default=0)
    draws = Column(Integer, default=0)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, onupdate=datetime.utcnow)
    last_used_at = Column(DateTime)
    
    # 关联关系
    training_session = db.relationship('TrainingSession', backref='model')
    evaluations = db.relationship('ModelEvaluation', backref='model', lazy='dynamic')
    
    def __repr__(self):
        return f'<AIModel {self.id}: {self.name} v{self.version}, Status: {self.status}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'version': self.version,
            'description': self.description,
            'status': self.status,
            'is_active': self.is_active,
            'file_path': self.file_path,
            'file_size': self.file_size,
            'checksum': self.checksum,
            'architecture': self.architecture,
            'input_size': self.input_size,
            'hidden_size': self.hidden_size,
            'num_layers': self.num_layers,
            'total_parameters': self.total_parameters,
            'performance_score': self.performance_score,
            'win_rate': self.win_rate,
            'draw_rate': self.draw_rate,
            'avg_thinking_time': self.avg_thinking_time,
            'training_session_id': self.training_session_id,
            'training_epochs': self.training_epochs,
            'training_loss': self.training_loss,
            'training_time': self.training_time,
            'total_games': self.total_games,
            'wins': self.wins,
            'losses': self.losses,
            'draws': self.draws,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_used_at': self.last_used_at.isoformat() if self.last_used_at else None
        }
    
    def update_performance(self, win_rate: float, draw_rate: float, avg_thinking_time: float):
        """更新性能指标"""
        self.win_rate = win_rate
        self.draw_rate = draw_rate
        self.avg_thinking_time = avg_thinking_time
        
        # 计算综合性能分数
        # 胜率权重70%，思考时间权重30%（时间越短越好）
        time_score = max(0, 1 - (avg_thinking_time / 10))  # 假设10秒为基准
        self.performance_score = win_rate * 0.7 + time_score * 0.3
        
        self.updated_at = datetime.utcnow()

class ModelEvaluation(db.Model):
    """模型评估表"""
    __tablename__ = 'model_evaluations'
    
    id = Column(Integer, primary_key=True)
    model_id = Column(Integer, db.ForeignKey('ai_models.id'), nullable=False)
    opponent_model_id = Column(Integer, db.ForeignKey('ai_models.id'))  # 对手模型ID，可为空
    
    # 评估配置
    total_games = Column(Integer, nullable=False)
    time_limit = Column(Float)  # 每步时间限制
    
    # 评估结果
    wins = Column(Integer, default=0)
    losses = Column(Integer, default=0)
    draws = Column(Integer, default=0)
    win_rate = Column(Float)
    
    # 详细统计
    avg_game_length = Column(Float)
    avg_thinking_time = Column(Float)
    total_thinking_time = Column(Float)
    
    # 状态和时间
    status = Column(String(20), default='running')  # 'running', 'completed', 'failed'
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    # 错误信息
    error_message = Column(Text)
    
    def __repr__(self):
        return f'<ModelEvaluation {self.id}: Model {self.model_id} vs {self.opponent_model_id or "Random"}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'model_id': self.model_id,
            'opponent_model_id': self.opponent_model_id,
            'total_games': self.total_games,
            'time_limit': self.time_limit,
            'wins': self.wins,
            'losses': self.losses,
            'draws': self.draws,
            'win_rate': self.win_rate,
            'avg_game_length': self.avg_game_length,
            'avg_thinking_time': self.avg_thinking_time,
            'total_thinking_time': self.total_thinking_time,
            'status': self.status,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_message': self.error_message
        }
