"""
AI模型管理API端点
"""
from flask import request, jsonify, send_file
from app.api import api_bp
from app.models.model import AIModel
from app import db
from ai.model.manager import ModelManager
from datetime import datetime
import os

model_manager = ModelManager()

@api_bp.route('/models', methods=['GET'])
def list_models():
    """获取模型列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        models = AIModel.query.order_by(AIModel.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'models': [
                {
                    'id': model.id,
                    'name': model.name,
                    'version': model.version,
                    'status': model.status,
                    'performance_score': model.performance_score,
                    'win_rate': model.win_rate,
                    'file_size': model.file_size,
                    'created_at': model.created_at.isoformat(),
                    'is_active': model.is_active
                } for model in models.items
            ],
            'total': models.total,
            'pages': models.pages,
            'current_page': page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models/<int:model_id>', methods=['GET'])
def get_model(model_id):
    """获取模型详情"""
    try:
        model = AIModel.query.get_or_404(model_id)
        
        return jsonify({
            'id': model.id,
            'name': model.name,
            'version': model.version,
            'description': model.description,
            'status': model.status,
            'performance_score': model.performance_score,
            'win_rate': model.win_rate,
            'total_games': model.total_games,
            'file_path': model.file_path,
            'file_size': model.file_size,
            'created_at': model.created_at.isoformat(),
            'updated_at': model.updated_at.isoformat() if model.updated_at else None,
            'is_active': model.is_active,
            'training_session_id': model.training_session_id
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models/<int:model_id>/activate', methods=['POST'])
def activate_model(model_id):
    """激活模型（设为当前使用的模型）"""
    try:
        model = AIModel.query.get_or_404(model_id)
        
        if model.status != 'ready':
            return jsonify({'error': 'Model is not ready for activation'}), 400
        
        # 停用其他模型
        AIModel.query.update({'is_active': False})
        
        # 激活当前模型
        model.is_active = True
        model.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        # 加载模型到内存
        success = model_manager.load_model(model.file_path)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'Model {model.name} activated successfully'
            })
        else:
            model.is_active = False
            db.session.commit()
            return jsonify({'error': 'Failed to load model'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models/<int:model_id>/evaluate', methods=['POST'])
def evaluate_model(model_id):
    """评估模型性能"""
    try:
        model = AIModel.query.get_or_404(model_id)
        
        if model.status != 'ready':
            return jsonify({'error': 'Model is not ready for evaluation'}), 400
        
        data = request.get_json()
        num_games = data.get('num_games', 100)
        opponent_model_id = data.get('opponent_model_id')
        
        # 启动评估任务
        evaluation_id = model_manager.start_evaluation(
            model_id, num_games, opponent_model_id
        )
        
        return jsonify({
            'evaluation_id': evaluation_id,
            'status': 'started',
            'message': 'Model evaluation started'
        }), 202
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models/<int:model_id>/download', methods=['GET'])
def download_model(model_id):
    """下载模型文件"""
    try:
        model = AIModel.query.get_or_404(model_id)
        
        if not os.path.exists(model.file_path):
            return jsonify({'error': 'Model file not found'}), 404
        
        return send_file(
            model.file_path,
            as_attachment=True,
            download_name=f'{model.name}_v{model.version}.pth'
        )
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models/<int:model_id>', methods=['DELETE'])
def delete_model(model_id):
    """删除模型"""
    try:
        model = AIModel.query.get_or_404(model_id)
        
        if model.is_active:
            return jsonify({'error': 'Cannot delete active model'}), 400
        
        # 删除模型文件
        if os.path.exists(model.file_path):
            os.remove(model.file_path)
        
        # 删除数据库记录
        db.session.delete(model)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Model deleted successfully'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models/active', methods=['GET'])
def get_active_model():
    """获取当前激活的模型"""
    try:
        model = AIModel.query.filter_by(is_active=True).first()
        
        if not model:
            return jsonify({'error': 'No active model found'}), 404
        
        return jsonify({
            'id': model.id,
            'name': model.name,
            'version': model.version,
            'performance_score': model.performance_score,
            'win_rate': model.win_rate,
            'created_at': model.created_at.isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
