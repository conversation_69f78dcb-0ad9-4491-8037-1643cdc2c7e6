/**
 * 游戏逻辑管理器
 */

class GameManager {
    constructor() {
        this.gameId = null;
        this.gameState = 'setup'; // 'setup', 'playing', 'finished'
        this.currentPlayer = 'black';
        this.playerType = 'ai'; // 'ai', 'human'
        this.difficulty = 'medium';
        this.moveCount = 0;
        this.gameStartTime = null;
        this.gameTimer = null;
        
        this.board = null;
        this.isMyTurn = true;
        this.isWaitingForAI = false;
        
        this.init();
    }
    
    init() {
        this.setupEventHandlers();
        this.setupWebSocketEvents();
        this.initializeBoard();
    }
    
    setupEventHandlers() {
        // 游戏设置
        document.getElementById('startGameBtn').addEventListener('click', () => {
            this.startNewGame();
        });
        
        document.getElementById('newGameBtn').addEventListener('click', () => {
            this.showGameSetup();
        });
        
        document.getElementById('surrenderBtn').addEventListener('click', () => {
            this.surrender();
        });
        
        document.getElementById('hintBtn').addEventListener('click', () => {
            this.requestHint();
        });
        
        document.getElementById('undoBtn').addEventListener('click', () => {
            this.undoMove();
        });
        
        // 模态框
        document.getElementById('newGameFromResult').addEventListener('click', () => {
            this.hideModal('gameResultModal');
            this.showGameSetup();
        });
        
        document.getElementById('backToSetup').addEventListener('click', () => {
            this.hideModal('gameResultModal');
            this.showGameSetup();
        });
        
        document.getElementById('closeHint').addEventListener('click', () => {
            this.hideModal('hintModal');
        });
        
        // 对手类型变化
        document.getElementById('playerType').addEventListener('change', (e) => {
            const difficultyGroup = document.getElementById('difficultyGroup');
            if (e.target.value === 'ai') {
                difficultyGroup.style.display = 'block';
            } else {
                difficultyGroup.style.display = 'none';
            }
        });
    }
    
    setupWebSocketEvents() {
        wsManager.on('gameJoined', (data) => {
            this.onGameJoined(data);
        });
        
        wsManager.on('moveMade', (data) => {
            this.onMoveMade(data);
        });
        
        wsManager.on('aiMove', (data) => {
            this.onAIMove(data);
        });
        
        wsManager.on('gameFinished', (data) => {
            this.onGameFinished(data);
        });
        
        wsManager.on('hintReceived', (data) => {
            this.onHintReceived(data);
        });
        
        wsManager.on('error', (data) => {
            this.showError(data.message);
            this.hideLoading();
        });
    }
    
    initializeBoard() {
        this.board = new GameBoard('gameBoard');
        
        // 设置棋盘点击事件
        this.board.onCellClick = (x, y) => {
            this.makeMove(x, y);
        };
        
        this.board.onCellHover = (x, y, isEnter) => {
            // 可以在这里添加悬停效果
        };
    }
    
    async startNewGame() {
        try {
            this.showLoading('创建游戏中...');
            
            // 获取游戏设置
            this.playerType = document.getElementById('playerType').value;
            this.difficulty = document.getElementById('difficulty').value;
            
            // 创建游戏
            const response = await fetch('/api/games', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    player_type: this.playerType,
                    difficulty: this.difficulty
                })
            });
            
            if (!response.ok) {
                throw new Error('Failed to create game');
            }
            
            const gameData = await response.json();
            this.gameId = gameData.game_id;
            
            // 加入游戏房间
            wsManager.joinGame(this.gameId);
            
        } catch (error) {
            this.showError('创建游戏失败: ' + error.message);
            this.hideLoading();
        }
    }
    
    onGameJoined(data) {
        this.hideLoading();
        this.gameState = 'playing';
        this.currentPlayer = data.current_player;
        this.moveCount = 0;
        
        // 显示游戏界面
        this.showGameArea();
        
        // 重置棋盘
        this.board.clearBoard();
        this.board.setCurrentPlayer(this.currentPlayer === 'black' ? 1 : 2);
        this.board.setInteractive(this.isMyTurn);
        
        // 设置玩家信息
        this.updatePlayerInfo();
        
        // 开始计时
        this.startGameTimer();
        
        // 更新UI状态
        this.updateGameControls();
        this.updateTurnIndicator();
    }
    
    makeMove(x, y) {
        if (!this.isMyTurn || this.gameState !== 'playing' || this.isWaitingForAI) {
            return;
        }
        
        // 检查位置是否有效
        if (this.board.boardState[x][y] !== 0) {
            return;
        }
        
        this.showLoading('发送移动...');
        
        // 发送移动到服务器
        wsManager.makeMove(this.gameId, x, y);
    }
    
    onMoveMade(data) {
        this.hideLoading();
        
        // 在棋盘上放置棋子
        const player = data.player === 'black' ? 1 : 2;
        this.board.placeStone(data.x, data.y, player);
        
        this.moveCount++;
        this.updateMoveCount();
        
        // 更新当前玩家
        this.currentPlayer = data.next_player;
        this.board.setCurrentPlayer(this.currentPlayer === 'black' ? 1 : 2);
        
        // 检查游戏状态
        if (data.game_status === 'finished') {
            this.onGameFinished(data);
        } else {
            // 更新回合指示器
            this.updateTurnIndicator();
            
            // 如果是AI回合，等待AI移动
            if (this.playerType === 'ai' && this.currentPlayer === 'white') {
                this.isWaitingForAI = true;
                this.board.setInteractive(false);
                this.showLoading('AI思考中...');
            } else {
                this.isMyTurn = true;
                this.board.setInteractive(true);
            }
        }
        
        // 添加到移动历史
        this.addMoveToHistory(data);
    }
    
    onAIMove(data) {
        this.hideLoading();
        this.isWaitingForAI = false;
        
        // 在棋盘上放置AI棋子
        const player = data.player === 'black' ? 1 : 2;
        this.board.placeStone(data.x, data.y, player);
        
        this.moveCount++;
        this.updateMoveCount();
        
        // 轮到玩家
        this.currentPlayer = 'black';
        this.board.setCurrentPlayer(1);
        this.isMyTurn = true;
        this.board.setInteractive(true);
        
        this.updateTurnIndicator();
        
        // 添加到移动历史
        this.addMoveToHistory({
            x: data.x,
            y: data.y,
            player: data.player,
            thinking_time: data.thinking_time
        });
    }
    
    onGameFinished(data) {
        this.gameState = 'finished';
        this.board.setInteractive(false);
        this.stopGameTimer();
        
        // 高亮获胜棋子（如果有）
        if (data.winner && data.winner !== 'draw') {
            // 这里可以添加获胜线高亮逻辑
        }
        
        // 显示游戏结果
        this.showGameResult(data);
        
        // 更新控制按钮
        this.updateGameControls();
    }
    
    requestHint() {
        if (this.gameState !== 'playing' || !this.isMyTurn) {
            return;
        }
        
        this.showLoading('获取提示中...');
        wsManager.requestHint(this.gameId);
    }
    
    onHintReceived(data) {
        this.hideLoading();
        
        // 在棋盘上显示提示
        this.board.showHint(data.x, data.y, data.confidence);
        
        // 显示提示模态框
        document.getElementById('hintMessage').textContent = data.reasoning || '这是AI建议的最佳位置';
        document.getElementById('hintPosition').textContent = `(${data.x + 1}, ${data.y + 1})`;
        document.getElementById('hintConfidence').textContent = `${(data.confidence * 100).toFixed(1)}%`;
        
        this.showModal('hintModal');
        
        // 3秒后自动隐藏提示
        setTimeout(() => {
            this.board.hideHint();
        }, 3000);
    }
    
    surrender() {
        if (this.gameState !== 'playing') {
            return;
        }
        
        if (!confirm('确定要投降吗？')) {
            return;
        }
        
        wsManager.surrender(this.gameId);
    }
    
    undoMove() {
        // 这里可以实现悔棋功能
        // 需要服务器端支持
        this.showError('悔棋功能暂未实现');
    }
    
    showGameSetup() {
        this.gameState = 'setup';
        document.getElementById('gameSetup').style.display = 'block';
        document.getElementById('gameArea').style.display = 'none';
        
        // 重置游戏状态
        this.resetGame();
    }
    
    showGameArea() {
        document.getElementById('gameSetup').style.display = 'none';
        document.getElementById('gameArea').style.display = 'block';
    }
    
    resetGame() {
        this.gameId = null;
        this.gameState = 'setup';
        this.currentPlayer = 'black';
        this.moveCount = 0;
        this.isMyTurn = true;
        this.isWaitingForAI = false;
        
        if (this.board) {
            this.board.clearBoard();
        }
        
        this.stopGameTimer();
        this.clearMoveHistory();
    }
    
    updatePlayerInfo() {
        const whitePlayerName = document.getElementById('whitePlayerName');
        if (this.playerType === 'ai') {
            whitePlayerName.textContent = `白棋 (AI-${this.difficulty})`;
        } else {
            whitePlayerName.textContent = '白棋 (人类)';
        }
    }
    
    updateTurnIndicator() {
        const turnIndicator = document.getElementById('currentTurn');
        if (this.gameState === 'finished') {
            turnIndicator.textContent = '游戏结束';
        } else if (this.isWaitingForAI) {
            turnIndicator.textContent = 'AI思考中...';
        } else if (this.currentPlayer === 'black') {
            turnIndicator.textContent = '黑棋回合';
        } else {
            turnIndicator.textContent = '白棋回合';
        }
    }
    
    updateMoveCount() {
        document.getElementById('moveCount').textContent = this.moveCount;
    }
    
    updateGameControls() {
        const surrenderBtn = document.getElementById('surrenderBtn');
        const hintBtn = document.getElementById('hintBtn');
        const undoBtn = document.getElementById('undoBtn');
        
        const isPlaying = this.gameState === 'playing';
        
        surrenderBtn.disabled = !isPlaying;
        hintBtn.disabled = !isPlaying || !this.isMyTurn;
        undoBtn.disabled = !isPlaying || this.moveCount === 0;
    }
    
    startGameTimer() {
        this.gameStartTime = Date.now();
        this.gameTimer = setInterval(() => {
            this.updateGameTime();
        }, 1000);
    }
    
    stopGameTimer() {
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
            this.gameTimer = null;
        }
    }
    
    updateGameTime() {
        if (!this.gameStartTime) return;
        
        const elapsed = Math.floor((Date.now() - this.gameStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        
        document.getElementById('gameTime').textContent = 
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    addMoveToHistory(moveData) {
        const historyList = document.getElementById('historyList');
        const moveElement = document.createElement('div');
        moveElement.className = `history-item ${moveData.player}`;
        moveElement.textContent = `${this.moveCount}. (${moveData.x + 1}, ${moveData.y + 1})`;
        
        if (moveData.thinking_time) {
            moveElement.title = `思考时间: ${moveData.thinking_time.toFixed(2)}秒`;
        }
        
        historyList.appendChild(moveElement);
        historyList.scrollTop = historyList.scrollHeight;
    }
    
    clearMoveHistory() {
        document.getElementById('historyList').innerHTML = '';
    }
    
    showGameResult(data) {
        const modal = document.getElementById('gameResultModal');
        const title = document.getElementById('resultTitle');
        const message = document.getElementById('resultMessage');
        
        let resultText = '';
        if (data.winner === 'draw') {
            title.textContent = '平局';
            resultText = '游戏以平局结束';
        } else if (data.winner === 'black') {
            title.textContent = '黑棋获胜';
            resultText = '恭喜！您获得了胜利！';
        } else {
            title.textContent = '白棋获胜';
            resultText = this.playerType === 'ai' ? 'AI获得了胜利，继续努力！' : '白棋玩家获得了胜利！';
        }
        
        if (data.reason === 'surrender') {
            resultText += '（对手投降）';
        }
        
        message.textContent = resultText;
        this.showModal('gameResultModal');
    }
    
    showModal(modalId) {
        document.getElementById(modalId).style.display = 'block';
    }
    
    hideModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }
    
    showLoading(message = '加载中...') {
        document.getElementById('loadingMessage').textContent = message;
        document.getElementById('loadingIndicator').style.display = 'block';
    }
    
    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }
    
    showError(message) {
        // 这里可以实现更好的错误显示
        alert('错误: ' + message);
    }
}
