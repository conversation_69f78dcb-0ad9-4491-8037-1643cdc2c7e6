#!/usr/bin/env python3
"""
AI五子棋系统启动脚本
"""
import os
import sys
import subprocess
import time
import argparse
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✓ Python版本: {sys.version}")

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask', 'flask-socketio', 
        'sqlalchemy', 'flask-sqlalchemy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} (缺失)")
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def setup_environment():
    """设置环境"""
    # 创建必要的目录
    directories = [
        'data/models',
        'data/games', 
        'data/training',
        'logs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ 创建目录: {directory}")
    
    # 创建.env文件（如果不存在）
    env_file = Path('.env')
    if not env_file.exists():
        env_content = """# Flask配置
FLASK_APP=backend/run.py
FLASK_ENV=development
SECRET_KEY=dev-secret-key-change-in-production

# 数据库配置
DATABASE_URL=sqlite:///data/app.db

# AI训练配置
DEVICE=cpu
BATCH_SIZE=32
LEARNING_RATE=0.001
EPOCHS=1000

# 日志配置
LOG_LEVEL=INFO

# WebSocket配置
SOCKETIO_ASYNC_MODE=eventlet
"""
        env_file.write_text(env_content)
        print("✓ 创建.env配置文件")

def install_dependencies():
    """安装依赖"""
    print("正在安装Python依赖...")
    
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'backend/requirements.txt'
        ], check=True)
        print("✓ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 依赖安装失败: {e}")
        return False

def initialize_database():
    """初始化数据库"""
    print("正在初始化数据库...")
    
    try:
        # 添加backend目录到Python路径
        backend_path = Path(__file__).parent / 'backend'
        sys.path.insert(0, str(backend_path))
        
        from app.utils.database import init_database
        init_database()
        print("✓ 数据库初始化完成")
        return True
    except Exception as e:
        print(f"✗ 数据库初始化失败: {e}")
        return False

def start_server(host='0.0.0.0', port=5000, debug=False):
    """启动服务器"""
    print(f"正在启动服务器 {host}:{port}...")
    
    # 设置环境变量
    env = os.environ.copy()
    env['FLASK_APP'] = 'backend/run.py'
    env['FLASK_ENV'] = 'development' if debug else 'production'
    env['HOST'] = host
    env['PORT'] = str(port)
    
    try:
        # 启动Flask应用
        subprocess.run([
            sys.executable, 'backend/run.py'
        ], env=env, cwd=Path(__file__).parent)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"✗ 服务器启动失败: {e}")

def run_tests():
    """运行测试"""
    print("正在运行测试...")
    
    try:
        subprocess.run([
            sys.executable, '-m', 'pytest', 'tests/', '-v'
        ], check=True)
        print("✓ 所有测试通过")
        return True
    except subprocess.CalledProcessError:
        print("✗ 测试失败")
        return False
    except FileNotFoundError:
        print("✗ pytest未安装，跳过测试")
        return True

def main():
    parser = argparse.ArgumentParser(description='AI五子棋系统启动脚本')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=5000, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--install', action='store_true', help='安装依赖')
    parser.add_argument('--init-db', action='store_true', help='初始化数据库')
    parser.add_argument('--test', action='store_true', help='运行测试')
    parser.add_argument('--setup-only', action='store_true', help='仅设置环境，不启动服务器')
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("AI五子棋训练和对战系统")
    print("=" * 50)
    
    # 检查Python版本
    check_python_version()
    
    # 设置环境
    setup_environment()
    
    # 安装依赖（如果需要）
    if args.install:
        if not install_dependencies():
            sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装依赖包，或使用 --install 参数自动安装")
        sys.exit(1)
    
    # 初始化数据库（如果需要）
    if args.init_db:
        if not initialize_database():
            sys.exit(1)
    
    # 运行测试（如果需要）
    if args.test:
        if not run_tests():
            sys.exit(1)
    
    # 如果只是设置环境，则退出
    if args.setup_only:
        print("\n环境设置完成！")
        print(f"要启动服务器，请运行: python {__file__} --host {args.host} --port {args.port}")
        return
    
    print("\n" + "=" * 50)
    print("启动信息:")
    print(f"- 游戏界面: http://{args.host}:{args.port}")
    print(f"- 管理面板: http://{args.host}:{args.port}/admin")
    print(f"- API文档: http://{args.host}:{args.port}/api/docs")
    print("- 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 启动服务器
    start_server(args.host, args.port, args.debug)

if __name__ == '__main__':
    main()
