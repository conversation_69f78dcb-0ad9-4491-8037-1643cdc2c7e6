"""
AI训练相关API端点
"""
from flask import request, jsonify
from app.api import api_bp
from app.models.training import TrainingSession, TrainingConfig
from app import db
from ai.training.trainer import AITrainer
from datetime import datetime
import threading

# 全局训练器实例
trainer = None
training_thread = None

@api_bp.route('/training/start', methods=['POST'])
def start_training():
    """开始AI训练"""
    global trainer, training_thread
    
    try:
        if trainer and trainer.is_training:
            return jsonify({'error': 'Training is already in progress'}), 400
        
        data = request.get_json()
        
        # 创建训练配置
        config = TrainingConfig(
            epochs=data.get('epochs', 1000),
            batch_size=data.get('batch_size', 32),
            learning_rate=data.get('learning_rate', 0.001),
            self_play_games=data.get('self_play_games', 100),
            model_name=data.get('model_name', f'model_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        )
        
        # 创建训练会话
        session = TrainingSession(
            config_id=config.id,
            status='running',
            started_at=datetime.utcnow()
        )
        
        db.session.add(config)
        db.session.add(session)
        db.session.commit()
        
        # 启动训练
        trainer = AITrainer(session.id)
        training_thread = threading.Thread(target=trainer.start_training, args=(config,))
        training_thread.start()
        
        return jsonify({
            'session_id': session.id,
            'status': 'started',
            'message': 'Training started successfully'
        }), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/training/stop', methods=['POST'])
def stop_training():
    """停止AI训练"""
    global trainer
    
    try:
        if not trainer or not trainer.is_training:
            return jsonify({'error': 'No training in progress'}), 400
        
        trainer.stop_training()
        
        return jsonify({
            'status': 'stopped',
            'message': 'Training stopped successfully'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/training/status', methods=['GET'])
def get_training_status():
    """获取训练状态"""
    global trainer
    
    try:
        if not trainer:
            return jsonify({
                'is_training': False,
                'status': 'idle'
            })
        
        status = trainer.get_status()
        
        return jsonify({
            'is_training': trainer.is_training,
            'session_id': trainer.session_id,
            'current_epoch': status.get('current_epoch', 0),
            'total_epochs': status.get('total_epochs', 0),
            'current_loss': status.get('current_loss', 0),
            'win_rate': status.get('win_rate', 0),
            'games_played': status.get('games_played', 0),
            'elapsed_time': status.get('elapsed_time', 0),
            'estimated_remaining': status.get('estimated_remaining', 0)
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/training/sessions', methods=['GET'])
def list_training_sessions():
    """获取训练会话列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        sessions = TrainingSession.query.order_by(TrainingSession.started_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'sessions': [
                {
                    'id': session.id,
                    'status': session.status,
                    'started_at': session.started_at.isoformat(),
                    'completed_at': session.completed_at.isoformat() if session.completed_at else None,
                    'total_epochs': session.total_epochs,
                    'final_loss': session.final_loss,
                    'final_win_rate': session.final_win_rate,
                    'config': {
                        'epochs': session.config.epochs,
                        'batch_size': session.config.batch_size,
                        'learning_rate': session.config.learning_rate,
                        'model_name': session.config.model_name
                    } if session.config else None
                } for session in sessions.items
            ],
            'total': sessions.total,
            'pages': sessions.pages,
            'current_page': page
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/training/sessions/<int:session_id>', methods=['GET'])
def get_training_session(session_id):
    """获取特定训练会话详情"""
    try:
        session = TrainingSession.query.get_or_404(session_id)
        
        return jsonify({
            'id': session.id,
            'status': session.status,
            'started_at': session.started_at.isoformat(),
            'completed_at': session.completed_at.isoformat() if session.completed_at else None,
            'total_epochs': session.total_epochs,
            'final_loss': session.final_loss,
            'final_win_rate': session.final_win_rate,
            'config': {
                'epochs': session.config.epochs,
                'batch_size': session.config.batch_size,
                'learning_rate': session.config.learning_rate,
                'self_play_games': session.config.self_play_games,
                'model_name': session.config.model_name
            } if session.config else None,
            'metrics': [
                {
                    'epoch': metric.epoch,
                    'loss': metric.loss,
                    'win_rate': metric.win_rate,
                    'timestamp': metric.timestamp.isoformat()
                } for metric in session.metrics
            ]
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
