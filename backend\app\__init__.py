"""
Flask应用工厂函数
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_socketio import SocketIO
from flask_cors import CORS
from config.config import config
import os

# 扩展实例
db = SQLAlchemy()
socketio = SocketIO()

def create_app(config_name=None):
    """创建Flask应用实例"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app = Flask(__name__, 
                static_folder='../../frontend/static',
                template_folder='../../frontend/templates')
    
    # 加载配置
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    socketio.init_app(app, 
                     cors_allowed_origins="*",
                     async_mode=app.config['SOCKETIO_ASYNC_MODE'])
    CORS(app)
    
    # 注册蓝图
    from app.api import api_bp
    from app.websocket import websocket_bp
    
    app.register_blueprint(api_bp, url_prefix='/api')
    app.register_blueprint(websocket_bp)
    
    # 注册路由
    from app import routes
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
    
    return app
