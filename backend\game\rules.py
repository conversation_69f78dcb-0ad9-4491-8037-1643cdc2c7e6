"""
五子棋游戏规则
"""
from typing import List, Tuple, Optional, Dict
from game.board import Board, Player
import numpy as np

class GameRules:
    """五子棋游戏规则类"""
    
    def __init__(self, board_size: int = 15, win_length: int = 5):
        """
        初始化游戏规则
        
        Args:
            board_size: 棋盘大小
            win_length: 获胜所需连子数
        """
        self.board_size = board_size
        self.win_length = win_length
    
    def is_valid_move(self, board: Board, x: int, y: int) -> bool:
        """
        验证移动是否有效
        
        Args:
            board: 棋盘对象
            x, y: 移动位置
            
        Returns:
            bool: 移动是否有效
        """
        return board.is_valid_position(x, y) and board.is_empty(x, y)
    
    def check_win_condition(self, board: Board, x: int, y: int, player: Player) -> bool:
        """
        检查在指定位置落子后是否获胜
        
        Args:
            board: 棋盘对象
            x, y: 落子位置
            player: 玩家
            
        Returns:
            bool: 是否获胜
        """
        # 临时落子
        original_value = board.board[x][y]
        board.board[x][y] = player.value
        
        # 检查四个方向
        directions = [(0, 1), (1, 0), (1, 1), (1, -1)]
        
        for dx, dy in directions:
            count = 1  # 当前位置
            
            # 向前计数
            nx, ny = x + dx, y + dy
            while (board.is_valid_position(nx, ny) and 
                   board.board[nx][ny] == player.value):
                count += 1
                nx += dx
                ny += dy
            
            # 向后计数
            nx, ny = x - dx, y - dy
            while (board.is_valid_position(nx, ny) and 
                   board.board[nx][ny] == player.value):
                count += 1
                nx -= dx
                ny -= dy
            
            # 恢复原值
            board.board[x][y] = original_value
            
            if count >= self.win_length:
                return True
        
        # 恢复原值
        board.board[x][y] = original_value
        return False
    
    def get_winning_line(self, board: Board, player: Player) -> Optional[List[Tuple[int, int]]]:
        """
        获取获胜线（如果存在）
        
        Args:
            board: 棋盘对象
            player: 玩家
            
        Returns:
            Optional[List[Tuple[int, int]]]: 获胜线的坐标列表
        """
        directions = [(0, 1), (1, 0), (1, 1), (1, -1)]
        
        for x in range(self.board_size):
            for y in range(self.board_size):
                if board.board[x][y] != player.value:
                    continue
                
                for dx, dy in directions:
                    line = [(x, y)]
                    
                    # 向前搜索
                    nx, ny = x + dx, y + dy
                    while (board.is_valid_position(nx, ny) and 
                           board.board[nx][ny] == player.value):
                        line.append((nx, ny))
                        nx += dx
                        ny += dy
                    
                    # 向后搜索
                    nx, ny = x - dx, y - dy
                    while (board.is_valid_position(nx, ny) and 
                           board.board[nx][ny] == player.value):
                        line.insert(0, (nx, ny))
                        nx -= dx
                        ny -= dy
                    
                    if len(line) >= self.win_length:
                        return line[:self.win_length]
        
        return None
    
    def evaluate_position(self, board: Board, player: Player) -> float:
        """
        评估当前局面对指定玩家的优势
        
        Args:
            board: 棋盘对象
            player: 玩家
            
        Returns:
            float: 评估分数，正数表示优势，负数表示劣势
        """
        opponent = Player.WHITE if player == Player.BLACK else Player.BLACK
        
        player_score = self._calculate_player_score(board, player)
        opponent_score = self._calculate_player_score(board, opponent)
        
        return player_score - opponent_score
    
    def _calculate_player_score(self, board: Board, player: Player) -> float:
        """
        计算单个玩家的分数
        
        Args:
            board: 棋盘对象
            player: 玩家
            
        Returns:
            float: 玩家分数
        """
        score = 0
        directions = [(0, 1), (1, 0), (1, 1), (1, -1)]
        
        # 评分权重
        weights = {
            5: 100000,  # 五连
            4: 10000,   # 活四
            3: 1000,    # 活三
            2: 100,     # 活二
            1: 10       # 单子
        }
        
        for x in range(self.board_size):
            for y in range(self.board_size):
                if board.board[x][y] == player.value:
                    for dx, dy in directions:
                        line_score = self._evaluate_line(board, x, y, dx, dy, player)
                        score += line_score
        
        return score
    
    def _evaluate_line(self, board: Board, x: int, y: int, dx: int, dy: int, player: Player) -> float:
        """
        评估从指定位置开始的一条线
        
        Args:
            board: 棋盘对象
            x, y: 起始位置
            dx, dy: 方向向量
            player: 玩家
            
        Returns:
            float: 线的评估分数
        """
        count = 0
        blocked = 0
        
        # 向前计数
        nx, ny = x, y
        while (board.is_valid_position(nx, ny) and 
               board.board[nx][ny] == player.value):
            count += 1
            nx += dx
            ny += dy
        
        # 检查是否被阻挡
        if (board.is_valid_position(nx, ny) and 
            board.board[nx][ny] != Player.EMPTY.value):
            blocked += 1
        
        # 向后检查
        nx, ny = x - dx, y - dy
        if (board.is_valid_position(nx, ny) and 
            board.board[nx][ny] != Player.EMPTY.value and
            board.board[nx][ny] != player.value):
            blocked += 1
        
        # 根据连子数和阻挡情况评分
        if count >= 5:
            return 100000
        elif count == 4:
            return 10000 if blocked == 0 else 1000
        elif count == 3:
            return 1000 if blocked == 0 else 100
        elif count == 2:
            return 100 if blocked == 0 else 10
        else:
            return 1
    
    def find_threats(self, board: Board, player: Player) -> List[Tuple[int, int]]:
        """
        找到对手的威胁位置（需要防守的位置）
        
        Args:
            board: 棋盘对象
            player: 当前玩家
            
        Returns:
            List[Tuple[int, int]]: 威胁位置列表
        """
        opponent = Player.WHITE if player == Player.BLACK else Player.BLACK
        threats = []
        
        for x, y in board.get_valid_moves():
            if self.check_win_condition(board, x, y, opponent):
                threats.append((x, y))
        
        return threats
    
    def find_winning_moves(self, board: Board, player: Player) -> List[Tuple[int, int]]:
        """
        找到获胜移动
        
        Args:
            board: 棋盘对象
            player: 玩家
            
        Returns:
            List[Tuple[int, int]]: 获胜移动列表
        """
        winning_moves = []
        
        for x, y in board.get_valid_moves():
            if self.check_win_condition(board, x, y, player):
                winning_moves.append((x, y))
        
        return winning_moves
    
    def is_draw(self, board: Board) -> bool:
        """
        检查是否平局
        
        Args:
            board: 棋盘对象
            
        Returns:
            bool: 是否平局
        """
        return len(board.get_valid_moves()) == 0 and board.check_winner() is None
